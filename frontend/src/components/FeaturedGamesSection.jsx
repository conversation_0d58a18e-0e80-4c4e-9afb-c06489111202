import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import GameCard from './GameCard';
import HorizontalGameScroller from './HorizontalGameScroller';
import { useLanguage } from '../context/LanguageContext';

/**
 * FeaturedGamesSection Component - Displays featured games in a horizontal scrollable layout
 * @param {Array} games - Array of featured games
 * @param {boolean} loading - Loading state
 * @param {string} className - Additional CSS classes
 */
const FeaturedGamesSection = ({ games = [], loading = false, className = '' }) => {
  const { t } = useLanguage();
  const [featuredGames, setFeaturedGames] = useState([]);

  // Process games to add featured status and select top games
  useEffect(() => {
    if (!games || games.length === 0) return;

    // Take the first 8-10 games and mark them as featured
    const processedGames = games.slice(0, 10).map(game => ({
      ...game,
      status: game.status || 'FEATURED' // Add featured status if not already present
    }));

    setFeaturedGames(processedGames);
  }, [games]);

  // Render individual game card
  const renderFeaturedGame = (game, index) => (
    <div className="h-full animate-fadeIn" style={{ animationDelay: `${index * 0.1}s` }}>
      <GameCard 
        game={game} 
        size="featured"
        className="transform hover:scale-105 transition-transform duration-300"
      />
    </div>
  );

  if (loading) {
    return (
      <section className={`mb-16 ${className}`}>
        <div className="flex items-center mb-8">
          <h2 className="text-3xl font-extrabold uppercase tracking-wide text-white pr-6 text-transparent bg-clip-text bg-gradient-to-r from-[#ff4500] to-[#ff8c00] drop-shadow-[0_0_15px_rgba(255,69,0,0.5)]">
            {t('homepage.sections.featuredGames')}
          </h2>
          <div className="h-1 flex-grow bg-gradient-to-r from-[rgba(255,69,0,0.8)] via-[rgba(255,140,0,0.3)] to-transparent rounded-full"></div>
        </div>
        
        {/* Loading skeleton */}
        <div className="flex gap-4 overflow-hidden">
          {[...Array(5)].map((_, index) => (
            <div key={index} className="flex-shrink-0 w-80 h-80 bg-gradient-to-br from-[#1a1a1a] to-[#0d0d0d] rounded-xl animate-pulse">
              <div className="w-full h-60 bg-gray-700 rounded-t-xl"></div>
              <div className="p-5 space-y-3">
                <div className="h-6 bg-gray-700 rounded w-3/4"></div>
                <div className="h-4 bg-gray-700 rounded w-1/2"></div>
                <div className="flex gap-2">
                  <div className="h-6 bg-gray-700 rounded w-16"></div>
                  <div className="h-6 bg-gray-700 rounded w-16"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>
    );
  }

  if (!featuredGames || featuredGames.length === 0) {
    return null;
  }

  return (
    <section className={`mb-16 ${className}`}>
      {/* Section Header */}
      <div className="flex items-center mb-8">
        <h2 className="text-3xl font-extrabold uppercase tracking-wide text-white pr-6 text-transparent bg-clip-text bg-gradient-to-r from-[#ff4500] to-[#ff8c00] drop-shadow-[0_0_15px_rgba(255,69,0,0.5)]">
          {t('homepage.sections.featuredGames')}
        </h2>
        <div className="h-1 flex-grow bg-gradient-to-r from-[rgba(255,69,0,0.8)] via-[rgba(255,140,0,0.3)] to-transparent rounded-full"></div>
      </div>

      {/* Featured Games Horizontal Scroller */}
      <HorizontalGameScroller
        games={featuredGames}
        renderGame={renderFeaturedGame}
        itemWidth={320}
        gap={16}
        showArrows={true}
        className="px-6" // Add padding for arrows
      />
    </section>
  );
};

FeaturedGamesSection.propTypes = {
  games: PropTypes.array,
  loading: PropTypes.bool,
  className: PropTypes.string
};

export default FeaturedGamesSection;
