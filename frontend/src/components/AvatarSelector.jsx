import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useAuth } from '../context/AuthContext';

const AvatarSelector = ({ onAvatarChange }) => {
  const { user } = useAuth();
  const [selectedAvatar, setSelectedAvatar] = useState(null);
  const [loading, setLoading] = useState(false);
  const [feedback, setFeedback] = useState({ message: '', type: '' });

  const S3_AVATARS_BASE_URL = 'https://indierepo.s3.us-east-1.amazonaws.com';

  const AVAILABLE_AVATARS = [
    {
      id: 1,
      filename: '1.png',
      url: `${S3_AVATARS_BASE_URL}/avatar/1.png`,
      name: 'Avatar 1'
    },
    {
      id: 2,
      filename: '2.png',
      url: `${S3_AVATARS_BASE_URL}/avatar/2.png`,
      name: 'Avatar 2'
    }
  ];

  useEffect(() => {
    if (user?.profileImage) {
      const currentAvatar = AVAILABLE_AVATARS.find(avatar => avatar.url === user.profileImage);
      if (currentAvatar) {
        setSelectedAvatar(currentAvatar);
      }
    }
  }, [user?.profileImage]);

  const handleAvatarSelect = (avatar) => {
    setSelectedAvatar(avatar);
    setFeedback({ message: '', type: '' });
  };

  const handleSaveAvatar = async () => {
    if (!selectedAvatar) {
      setFeedback({ message: 'Please select an avatar', type: 'error' });
      return;
    }

    if (selectedAvatar.url === user?.profileImage) {
      setFeedback({ message: 'This is already your current avatar', type: 'info' });
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/users/select-avatar', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          avatarId: selectedAvatar.id,
          avatarUrl: selectedAvatar.url
        })
      });

      const data = await response.json();

      if (response.ok) {
        setFeedback({ message: 'Avatar updated successfully!', type: 'success' });
        
        if (onAvatarChange) {
          onAvatarChange(selectedAvatar);
        }

        // Reload to refresh user data
        window.location.reload();
      } else {
        setFeedback({ message: data.message || 'Failed to update avatar', type: 'error' });
      }
    } catch (error) {
      console.error('Avatar update error:', error);
      setFeedback({ message: 'Error updating avatar', type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-gray-700/30 p-6 rounded-lg border border-gray-600">
        <h2 className="text-xl font-semibold text-white mb-6 pb-3 border-b border-gray-600">
          Choose Your Avatar
        </h2>
        
        <div className="flex items-center gap-4 mb-6">
          <div className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center overflow-hidden border-2 border-gray-500">
            {user?.profileImage ? (
              <img 
                src={user.profileImage} 
                alt="Current avatar" 
                className="w-full h-full object-cover rounded-full" 
              />
            ) : (
              <div className="text-gray-400 text-sm">No Avatar</div>
            )}
          </div>
          <div>
            <p className="text-white font-medium">Current Avatar</p>
            <p className="text-gray-400 text-sm">Choose from the available options below</p>
          </div>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-6">
          {AVAILABLE_AVATARS.map((avatar) => (
            <div
              key={avatar.id}
              className={`relative cursor-pointer transition-all duration-200 rounded-lg p-3 border-2 ${
                selectedAvatar?.id === avatar.id
                  ? 'border-red-500 bg-red-500/10'
                  : 'border-gray-600 hover:border-gray-400 bg-gray-800/50'
              }`}
              onClick={() => handleAvatarSelect(avatar)}
            >
              <div className="w-16 h-16 mx-auto rounded-full overflow-hidden border-2 border-gray-500">
                <img
                  src={avatar.url}
                  alt={avatar.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'flex';
                  }}
                />
                <div 
                  className="w-full h-full bg-gray-600 hidden items-center justify-center text-xs text-gray-400"
                >
                  Error
                </div>
              </div>
              <p className="text-center text-sm text-gray-300 mt-2">{avatar.name}</p>
              
              {selectedAvatar?.id === avatar.id && (
                <div className="absolute top-1 right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">✓</span>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="flex justify-between items-center">
          <button
            onClick={handleSaveAvatar}
            disabled={loading || !selectedAvatar}
            className="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Updating...' : 'Save Avatar'}
          </button>

          <p className="text-gray-400 text-sm">
            Select from our curated avatar collection
          </p>
        </div>

        {feedback.message && (
          <div className={`mt-4 p-3 rounded-lg ${
            feedback.type === 'success' 
              ? 'bg-green-500/20 border border-green-500/50 text-green-300'
              : feedback.type === 'error'
              ? 'bg-red-500/20 border border-red-500/50 text-red-300'
              : 'bg-blue-500/20 border border-blue-500/50 text-blue-300'
          }`}>
            {feedback.message}
          </div>
        )}
      </div>
    </div>
  );
};

AvatarSelector.propTypes = {
  onAvatarChange: PropTypes.func
};

export default AvatarSelector; 