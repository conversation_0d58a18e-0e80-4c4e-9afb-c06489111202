import { useState, useEffect } from 'react';
import { FaTimes, FaLock, FaUnlock, FaCheckCircle, FaInfoCircle, FaGamepad, FaTools, FaCheck } from 'react-icons/fa';
import PropTypes from 'prop-types';
import PayPalButton from './PayPalButton';

/**
 * PurchaseModal Component - Modal for game purchase with payment options
 * @param {boolean} isOpen - Whether the modal is open
 * @param {Function} onClose - Callback to close the modal
 * @param {Object} game - Game object with price and details
 * @param {Function} onSuccess - Callback for successful payment
 * @param {Function} onError - Callback for payment errors
 * @param {Function} onCancel - Callback for payment cancellation
 * @param {boolean} processingPayment - Whether payment is being processed
 * @param {boolean} paymentSuccess - Whether payment was successful
 * @param {string} purchaseStatus - Current purchase status
 */
const PurchaseModal = ({ 
  isOpen, 
  onClose, 
  game, 
  onSuccess, 
  onError, 
  onCancel,
  processingPayment,
  paymentSuccess
}) => {
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [paymentCancelled, setPaymentCancelled] = useState(false);
  const [paymentError, setPaymentError] = useState(null);
  const [paypalLoading, setPaypalLoading] = useState(true);
  
  // Reset states when modal opens or closes
  useEffect(() => {
    if (isOpen) {
      setPaymentCancelled(false);
      setPaymentError(null);
      setPaypalLoading(true);
    }
  }, [isOpen]);
  
  // Simuliamo il caricamento completato di PayPal dopo un po'
  useEffect(() => {
    if (isOpen) {
      // Usiamo un timer per simulare il momento in cui PayPal è stato caricato
      const timer = setTimeout(() => {
        setPaypalLoading(false);
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, [isOpen]);
  
  if (!isOpen) return null;

  // Calculate number of locked files
  const lockedFiles = game.files ? game.files.filter(file => file.requiresPurchase).length : 0;
  
  // Format price with currency symbol
  const formattedPrice = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(game.price);
  
  // Enhanced handlers for payment events
  const handlePaymentSuccess = (data) => {
    
    onSuccess(data);
  };

  const handlePaymentError = (errorMsg) => {
    console.error('Payment error in modal:', errorMsg);
    setPaymentError(errorMsg);
    onError(errorMsg);
  };

  const handlePaymentCancel = () => {
    
    setPaymentCancelled(true);
    onCancel();
  };

  const handlePayPalLoad = () => {
    
    setPaypalLoading(false);
  };

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-[10000] p-4" onClick={onClose}>
      <div className="bg-[#1a1a1a] rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto relative shadow-2xl" onClick={e => e.stopPropagation()}>
        <button className="absolute top-4 right-4 text-white/70 hover:text-white text-xl p-2 z-10 transition-colors duration-200" onClick={onClose}>
          <FaTimes />
        </button>
        
        <div className="p-6 border-b border-gray-700">
          <h2 className="text-2xl font-bold text-white mb-3">Buy the Complete Game: {game.title}</h2>
          {!paymentSuccess ? (
            <div className="flex items-center gap-2 text-orange-400 bg-orange-400/10 px-3 py-2 rounded-lg">
              <FaLock /> {lockedFiles} Game Files to Unlock
            </div>
          ) : (
            <div className="flex items-center gap-2 text-green-400 bg-green-400/10 px-3 py-2 rounded-lg">
              <FaUnlock /> {lockedFiles} Files Unlocked
            </div>
          )}
        </div>
        
        {paymentSuccess ? (
          <div className="p-8 text-center">
            <div className="text-6xl text-green-400 mb-4">
              <FaCheckCircle />
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">Payment Successful!</h3>
            <p className="text-gray-300 mb-3">Thank you for your purchase. All game files are now unlocked and ready to download.</p>
            <p className="text-gray-300 mb-6">The game has been added to <strong className="text-white">My Profile &gt; My Games</strong> for future access.</p>
            <button className="bg-gradient-to-r from-[#22c55e] to-[#16a34a] text-white px-8 py-3 rounded-lg font-semibold hover:from-[#16a34a] hover:to-[#15803d] transition-all duration-300 shadow-lg" onClick={onClose}>
              Start Downloading
            </button>
          </div>
        ) : (
          <>
            <div className="p-6">
              <div className="mb-6">
                <div className="mb-6">
                  <h3 className="text-3xl font-bold text-white mb-2">{formattedPrice}</h3>
                  <p className="text-gray-300 mb-4">Purchase and unlock all game files:</p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div className="flex items-start gap-3 p-4 bg-[#2a2a2a] rounded-lg">
                      <div className="text-green-400 text-xl mt-1">
                        <FaUnlock />
                      </div>
                      <div>
                        <h4 className="text-white font-semibold mb-1">Complete Game</h4>
                        <p className="text-gray-400 text-sm">Access to all game files</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3 p-4 bg-[#2a2a2a] rounded-lg">
                      <div className="text-blue-400 text-xl mt-1">
                        <FaGamepad />
                      </div>
                      <div>
                        <h4 className="text-white font-semibold mb-1">In Your Library</h4>
                        <p className="text-gray-400 text-sm">Added to your personal library</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3 p-4 bg-[#2a2a2a] rounded-lg">
                      <div className="text-purple-400 text-xl mt-1">
                        <FaTools />
                      </div>
                      <div>
                        <h4 className="text-white font-semibold mb-1">Updates</h4>
                        <p className="text-gray-400 text-sm">Access to all future updates</p>
                      </div>
                    </div>
                    
                    {game.publisherName && (
                      <div className="flex items-start gap-3 p-4 bg-[#2a2a2a] rounded-lg">
                        <div className="text-green-400 text-xl mt-1">
                          <FaCheck />
                        </div>
                        <div>
                          <h4 className="text-white font-semibold mb-1">100% Developer Support</h4>
                          <p className="text-gray-400 text-sm">IndieRepo takes 0% commission unlike other platforms, 100% goes directly to {game.publisherName}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-start gap-3 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg mb-6">
                <FaInfoCircle className="text-blue-400 text-lg mt-0.5 flex-shrink-0" />
                <p className="text-gray-300 text-sm">After purchase, all game files will be unlocked immediately. A receipt will be sent to your email and the game will be added to <strong className="text-white">My Profile &gt; My Games</strong>.</p>
              </div>
              
              <div className="flex items-start gap-3 mb-6">
                <input 
                  type="checkbox" 
                  id="agree-terms" 
                  checked={agreedToTerms}
                  onChange={() => setAgreedToTerms(!agreedToTerms)}
                  className="mt-1 w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                />
                <label htmlFor="agree-terms" className="text-gray-300 text-sm">
                  I agree to the <a href="/terms" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300 underline">Terms and Conditions</a>
                </label>
              </div>
            </div>
            
            <div className="p-6 border-t border-gray-700">
              <h3 className="text-xl font-bold text-white mb-4">Payment Options</h3>
              
              {processingPayment ? (
                <div className="flex items-center justify-center gap-3 py-8">
                  <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  <span className="text-white">Processing payment...</span>
                </div>
              ) : (
                <>
                  <div className={`${!agreedToTerms ? 'opacity-50 pointer-events-none' : ''}`}>
                    {agreedToTerms ? (
                      <>
                        <div className="space-y-4">
                          <div className="relative">
                            {paypalLoading && (
                              <div className="flex items-center justify-center gap-3 p-4 bg-[#0070ba]/10 border border-[#0070ba]/20 rounded-lg">
                                <div className="w-8 h-8 bg-[#0070ba] rounded"></div>
                                <div className="flex-1">
                                  <div className="h-3 bg-gray-600 rounded mb-2 animate-pulse"></div>
                                  <div className="h-2 bg-gray-700 rounded w-3/4 animate-pulse"></div>
                                </div>
                              </div>
                            )}
                            <div className={`${paypalLoading ? 'opacity-0 absolute inset-0' : 'opacity-100'} transition-opacity duration-300`}>
                              <PayPalButton 
                                game={game}
                                onSuccess={handlePaymentSuccess}
                                onError={handlePaymentError}
                                onCancel={handlePaymentCancel}
                                onLoaded={handlePayPalLoad}
                                key={`paypal-${paymentCancelled ? 'cancelled' : 'fresh'}-${Date.now()}`}
                              />
                            </div>
                          </div>
                          
                          {/* Google Pay option with loading indicator */}
                          <div className="border border-gray-600 rounded-lg overflow-hidden">
                            <button 
                              className="w-full p-4 bg-white hover:bg-gray-50 transition-colors duration-200 flex items-center justify-center gap-3"
                              onClick={() => {
                                // Placeholder for Google Pay implementation
                        
                                alert('Google Pay integration coming soon!');
                              }}
                            >
                              <div className="flex items-center justify-center">
                                <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M19.2 10.5c0-.7-.1-1.4-.4-2H10v3.8h5.2c-.2 1.1-.9 2.1-1.9 2.8v2.3h3c1.8-1.7 2.9-4.1 2.9-6.9z" fill="#4285F4"/>
                                  <path d="M10 20c2.5 0 4.6-.8 6.2-2.2l-3-2.3c-.8.6-1.9.9-3.1.9-2.4 0-4.4-1.6-5.1-3.8H1.8v2.4c1.5 3 4.6 5 8.2 5z" fill="#34A853"/>
                                  <path d="M4.9 12.6c-.2-.6-.3-1.3-.3-2s.1-1.4.3-2V6.2H1.8C1.3 7.4 1 8.6 1 10s.3 2.6.8 3.8l3.1-1.2z" fill="#FBBC04"/>
                                  <path d="M10 4.6c1.4 0 2.6.5 3.5 1.3l2.6-2.6C14.5 1.9 12.4 1 10 1 6.4 1 3.3 3.1 1.8 6.2l3.1 2.4c.7-2.2 2.7-3.8 5.1-4z" fill="#EA4335"/>
                                </svg>
                              </div>
                              <span className="text-gray-800 font-medium">
                                <span className="text-gray-600">Pay with</span>
                                <span className="ml-1 font-semibold">Google Pay</span>
                              </span>
                            </button>
                          </div>
                        </div>
                        
                        {/* Payment cancelled message */}
                        {paymentCancelled && (
                          <div className="mt-4 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg text-yellow-400 text-sm text-center">
                            Payment was cancelled. Please try again using the options above.
                          </div>
                        )}
                      </>
                    ) : (
                      <div className="text-center py-8 text-gray-400">
                        Please agree to the terms and conditions to continue
                      </div>
                    )}
                  </div>
                  
                  {/* Show payment error outside the button container */}
                  {paymentError && (
                    <div className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm text-center">
                      {paymentError}
                    </div>
                  )}
                  
                  <p className="text-center text-gray-400 text-sm mt-6 flex items-center justify-center gap-2">
                    <span role="img" aria-label="secure">🔒</span> Safe & secure payment via PayPal or Google Pay
                  </p>
                </>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

PurchaseModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  game: PropTypes.shape({
    title: PropTypes.string.isRequired,
    price: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    files: PropTypes.arrayOf(PropTypes.shape({
      requiresPurchase: PropTypes.bool
    })),
    publisherName: PropTypes.string
  }).isRequired,
  onSuccess: PropTypes.func.isRequired,
  onError: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  processingPayment: PropTypes.bool,
  paymentSuccess: PropTypes.bool
};

PurchaseModal.defaultProps = {
  processingPayment: false,
  paymentSuccess: false
};

export default PurchaseModal;
