import { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FaUser, FaCog, FaSignOutAlt, FaGamepad, FaHeart } from 'react-icons/fa';
import PropTypes from 'prop-types';

/**
 * UserMenu Component - Displays user avatar and dropdown menu with navigation options
 * @param {Object} user - User object containing username and avatar
 * @param {Function} onLogout - Callback function for logout action
 */
const UserMenu = ({ user, onLogout }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const menuRef = useRef();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  // Close menu if clicked outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={menuRef}>
      <button 
        className="flex items-center bg-transparent border-none cursor-pointer py-2 px-3 text-white rounded transition-colors duration-300 hover:bg-white/10"
        onClick={toggleMenu}
      >
        <div className="w-[30px] h-[30px] rounded-full bg-[#555] flex items-center justify-center mr-2 overflow-hidden">
          {user.avatar ? (
            <img src={user.avatar} alt={user.username} className="w-full h-full object-cover" />
          ) : (
            <FaUser className="text-[#eee] text-[18px]" />
          )}
        </div>
        <span className="font-semibold max-w-[120px] text-ellipsis overflow-hidden whitespace-nowrap md:max-w-[80px]">{user.username}</span>
      </button>

      {isMenuOpen && (
        <div className="absolute top-full right-0 md:right-[-10px] bg-white rounded-md shadow-[0_4px_12px_rgba(0,0,0,0.1)] min-w-[200px] z-[1000] mt-[5px] overflow-hidden">
          <Link 
            to="/profile" 
            className="flex items-center py-3 px-4 text-[#333] no-underline transition-colors duration-200 text-[0.95rem] hover:bg-[#f5f5f5]" 
            onClick={() => setIsMenuOpen(false)}
          >
            <FaUser className="mr-2.5 text-[1.1rem] text-[#666]" /> My Profile
          </Link>
          <Link 
            to="/profile" 
            onClick={() => setIsMenuOpen(false)} 
            className="flex items-center py-3 px-4 text-[#333] no-underline transition-colors duration-200 text-[0.95rem] hover:bg-[#f5f5f5]" 
            state={{ section: 'library' }}
          >
            <FaGamepad className="mr-2.5 text-[1.1rem] text-[#666]" /> My Games
          </Link>
          <Link 
            to="/profile" 
            onClick={() => setIsMenuOpen(false)} 
            className="flex items-center py-3 px-4 text-[#333] no-underline transition-colors duration-200 text-[0.95rem] hover:bg-[#f5f5f5]" 
            state={{ section: 'wishlist' }}
          >
            <FaHeart className="mr-2.5 text-[1.1rem] text-[#666]" /> Wishlist
          </Link>
          <Link 
            to="/profile" 
            onClick={() => setIsMenuOpen(false)} 
            className="flex items-center py-3 px-4 text-[#333] no-underline transition-colors duration-200 text-[0.95rem] hover:bg-[#f5f5f5]" 
            state={{ section: 'settings' }}
          >
            <FaCog className="mr-2.5 text-[1.1rem] text-[#666]" /> Settings
          </Link>
          <div className="h-px bg-[#eee] my-1.5"></div>
          <button 
            className="w-full text-left bg-transparent border-none cursor-pointer text-[0.95rem] text-[#f44336] flex items-center py-3 px-4 transition-colors duration-200 hover:bg-[#fff5f5]" 
            onClick={onLogout}
          >
            <FaSignOutAlt className="mr-2.5 text-[1.1rem] text-[#f44336]" /> Logout
          </button>
        </div>
      )}
    </div>
  );
};

UserMenu.propTypes = {
  user: PropTypes.shape({
    username: PropTypes.string.isRequired,
    avatar: PropTypes.string
  }).isRequired,
  onLogout: PropTypes.func.isRequired
};

export default UserMenu;
