import { useEffect, useRef, useState } from 'react';
import { useAuth } from '../context/AuthContext';
import PropTypes from 'prop-types';
import { API_URL } from '../config/env.js';

/**
 * PayPalButton Component - Renders PayPal payment button with loading and error states
 * @param {Object} game - Game object containing price and title
 * @param {Function} onSuccess - Callback for successful payment
 * @param {Function} onError - Callback for payment errors
 * @param {Function} onCancel - Callback for payment cancellation
 * @param {Function} onLoaded - Callback when PayPal button is loaded
 */
const PayPalButton = ({ game, onSuccess, onError, onCancel, onLoaded }) => {
  const { user } = useAuth();
  const paypalContainerRef = useRef(null);
  const [sdkReady, setSdkReady] = useState(false);
  const [attemptCount, setAttemptCount] = useState(0);
  const [isRendered, setIsRendered] = useState(false);

  useEffect(() => {
    const loadPayPalScript = () => {
      // Clean up any existing script to avoid duplicates
      const existingScript = document.querySelector('script[src*="paypal.com/sdk"]');
      if (existingScript) {
        existingScript.remove();
      }

      const script = document.createElement('script');
      script.src = `https://www.paypal.com/sdk/js?client-id=${import.meta.env.VITE_PAYPAL_CLIENT_ID}&currency=USD`;
      script.async = true;
      
      script.onload = () => {

        setSdkReady(true);
      };
      
      script.onerror = (err) => {
        console.error('Error loading PayPal SDK:', err);
        onError('PayPal could not be loaded. Please disable ad-blockers or try another browser.');
      };
      
      document.body.appendChild(script);
    };

    // Load the PayPal script
    loadPayPalScript();
    
    // Cleanup function
    return () => {
      const script = document.querySelector('script[src*="paypal.com/sdk"]');
      if (script) {
        document.body.removeChild(script);
      }
    };
  }, [onError]);

  // Effect to render PayPal button when SDK is ready and container exists
  useEffect(() => {
    if (sdkReady && paypalContainerRef.current && window.paypal) {
      try {
        // Clear the container first
        paypalContainerRef.current.innerHTML = '';
        
        const button = window.paypal.Buttons({
          createOrder: (data, actions) => {
            return actions.order.create({
              purchase_units: [
                {
                  description: `Purchase of ${game.title}`,
                  amount: {
                    currency_code: 'USD',
                    value: game.price
                  },
                  custom_id: `game_${game.id}_user_${user?.id || 'guest'}`
                }
              ]
            });
          },
          onApprove: async (data, actions) => {
            // Capture the funds from the transaction
            const details = await actions.order.capture();
    
            onSuccess({
              orderId: details.id,
              status: details.status,
              payerEmail: details.payer.email_address
            });

            // Send confirmation to backend
            try {
              const response = await fetch(`${API_URL}/payments/confirm`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  orderId: data.orderID,
                  gameId: game.id,
                  userId: user?.id,
                  paymentDetails: details
                }),
                credentials: 'include'
              });
              
              const result = await response.json();
              
              if (result.success) {
                onSuccess(result);
              } else {
                onError('Payment validation failed');
              }
            } catch (error) {
              console.error('Payment confirmation error:', error);
              onError('Failed to confirm payment with server');
            }
          },
          onError: (err) => {
            console.error('PayPal error:', err);
            onError('Payment failed. Please try again.');
          },
          onCancel: () => {
  
            // Mark the button as rendered still to keep it visible
            setIsRendered(true);
            onCancel();
          }
        });

        if (button.isEligible()) {
          button.render(paypalContainerRef.current);
          setIsRendered(true);
          
          
          // Notifica il caricamento completato immediatamente senza setTimeout
          if (onLoaded) onLoaded();
        } else {
          console.error('PayPal button is not eligible for rendering');
          onError('PayPal checkout is not available. Please try a different payment method.');
        }
      } catch (error) {
        console.error('Error rendering PayPal button:', error);
        
        // Retry rendering if we haven't exceeded max attempts
        if (attemptCount < 3) {
          setTimeout(() => {
            setAttemptCount(prev => prev + 1);
          }, 1000);
        } else {
          onError('Could not load payment system. Please try again later.');
        }
      }
    }
  }, [sdkReady, game, onSuccess, onError, onCancel, attemptCount, user, onLoaded]);

  // Re-render button if it ever disappears
  useEffect(() => {
    // Check if container is empty but should have button
    if (isRendered && paypalContainerRef.current && 
        paypalContainerRef.current.children.length === 0 && sdkReady) {
      
      setIsRendered(false);
      setAttemptCount(attemptCount + 1);
    }
  }, [isRendered, sdkReady, attemptCount]);

  return (
    <div>
      <div 
        ref={paypalContainerRef} 
        className="w-full max-w-[400px] mx-auto my-[15px] min-h-[50px] flex items-center justify-center [&>div]:w-full [&>div]:min-h-[45px] [&_iframe]:z-10 [&_iframe]:opacity-100 [&_iframe]:visible"
      ></div>
      {!sdkReady && (
        <div className="flex items-center justify-center gap-2.5 bg-white/10 rounded py-2.5 px-2.5 text-white w-full h-[45px]">
          <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
          Loading payment options...
        </div>
      )}
    </div>
  );
};

PayPalButton.propTypes = {
  game: PropTypes.object.isRequired,
  onSuccess: PropTypes.func.isRequired,
  onError: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  onLoaded: PropTypes.func // Aggiunta questa prop
};

export default PayPalButton;
