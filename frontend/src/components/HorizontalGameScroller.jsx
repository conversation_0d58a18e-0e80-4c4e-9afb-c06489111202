import { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';

/**
 * HorizontalGameScroller Component - Horizontal scrollable container for games
 * @param {Array} games - Array of game objects
 * @param {Function} renderGame - Function to render each game item
 * @param {string} className - Additional CSS classes
 * @param {boolean} showArrows - Whether to show navigation arrows
 * @param {number} itemWidth - Width of each item in pixels
 * @param {number} gap - Gap between items in pixels
 */
const HorizontalGameScroller = ({ 
  games = [], 
  renderGame, 
  className = '', 
  showArrows = true,
  itemWidth = 320,
  gap = 16
}) => {
  const scrollContainerRef = useRef(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);

  // Check scroll position and update arrow states
  const checkScrollPosition = () => {
    if (!scrollContainerRef.current) return;
    
    const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
    setCanScrollLeft(scrollLeft > 0);
    setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
  };

  // Initialize scroll position check
  useEffect(() => {
    checkScrollPosition();
    
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollPosition);
      return () => container.removeEventListener('scroll', checkScrollPosition);
    }
  }, [games]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      checkScrollPosition();
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Scroll functions
  const scrollLeft = () => {
    if (!scrollContainerRef.current || isScrolling) return;
    
    setIsScrolling(true);
    const scrollAmount = itemWidth + gap;
    scrollContainerRef.current.scrollBy({
      left: -scrollAmount * 2, // Scroll 2 items at a time
      behavior: 'smooth'
    });
    
    setTimeout(() => setIsScrolling(false), 300);
  };

  const scrollRight = () => {
    if (!scrollContainerRef.current || isScrolling) return;
    
    setIsScrolling(true);
    const scrollAmount = itemWidth + gap;
    scrollContainerRef.current.scrollBy({
      left: scrollAmount * 2, // Scroll 2 items at a time
      behavior: 'smooth'
    });
    
    setTimeout(() => setIsScrolling(false), 300);
  };

  if (!games || games.length === 0) {
    return (
      <div className="flex items-center justify-center py-12 text-gray-400">
        <p>No games to display</p>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* Left Arrow */}
      {showArrows && canScrollLeft && (
        <button
          onClick={scrollLeft}
          disabled={isScrolling}
          className="absolute left-0 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-black/70 hover:bg-black/90 rounded-full flex items-center justify-center text-white transition-all duration-200 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
          aria-label="Scroll left"
        >
          <FaChevronLeft className="text-lg" />
        </button>
      )}

      {/* Right Arrow */}
      {showArrows && canScrollRight && (
        <button
          onClick={scrollRight}
          disabled={isScrolling}
          className="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-black/70 hover:bg-black/90 rounded-full flex items-center justify-center text-white transition-all duration-200 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
          aria-label="Scroll right"
        >
          <FaChevronRight className="text-lg" />
        </button>
      )}

      {/* Scrollable Container */}
      <div
        ref={scrollContainerRef}
        className="flex overflow-x-auto scrollbar-hide scroll-smooth"
        style={{ 
          gap: `${gap}px`,
          scrollbarWidth: 'none',
          msOverflowStyle: 'none'
        }}
      >
        {games.map((game, index) => (
          <div
            key={game.id || index}
            className="flex-shrink-0"
            style={{ width: `${itemWidth}px` }}
          >
            {renderGame(game, index)}
          </div>
        ))}
      </div>

      {/* Custom scrollbar styles */}
      <style jsx>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
};

HorizontalGameScroller.propTypes = {
  games: PropTypes.array.isRequired,
  renderGame: PropTypes.func.isRequired,
  className: PropTypes.string,
  showArrows: PropTypes.bool,
  itemWidth: PropTypes.number,
  gap: PropTypes.number
};

export default HorizontalGameScroller;
