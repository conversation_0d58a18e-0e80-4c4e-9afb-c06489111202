import PropTypes from 'prop-types';

const GameLoader = ({ 
  size = 'md', 
  className = '',
  fullScreen = false,
  variant = 'gamepad' // gamepad, controller, dice, pixel
}) => {
  // Size configurations
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-20 h-20'
  };

  const spinnerSize = sizeClasses[size] || sizeClasses.md;

  const GamepadLoader = () => (
    <div className={`${spinnerSize} ${className} relative`}>
      <svg 
        className="animate-pulse w-full h-full text-red-500"
        viewBox="0 0 24 24" 
        fill="currentColor"
      >
        <path d="M7.97 16c.85 0 1.61-.54 1.89-1.34L12 8.5l2.14 6.16c.28.8 1.04 1.34 1.89 1.34 1.85 0 3.36-1.5 3.36-3.35V9c0-3.31-2.69-6-6-6H10.61c-3.31 0-6 2.69-6 6v3.65c0 1.85 1.51 3.35 3.36 3.35zM9 11c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm6 0c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/>
      </svg>
      <div className="absolute inset-0 animate-spin">
        <div className="w-full h-full border-2 border-transparent border-t-orange-500 rounded-full"></div>
      </div>
    </div>
  );

  const ControllerLoader = () => (
    <div className={`${spinnerSize} ${className} relative`}>
      <svg 
        className="animate-bounce w-full h-full text-red-500"
        viewBox="0 0 24 24" 
        fill="currentColor"
      >
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
      </svg>
      <div className="absolute inset-0 animate-ping opacity-75">
        <div className="w-full h-full bg-orange-500 rounded-full"></div>
      </div>
    </div>
  );

  const DiceLoader = () => (
    <div className={`${spinnerSize} ${className}`}>
      <svg 
        className="animate-spin w-full h-full text-red-500"
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor" 
        strokeWidth="2"
      >
        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
        <circle cx="9" cy="9" r="1"/>
        <circle cx="15" cy="15" r="1"/>
        <circle cx="12" cy="12" r="1"/>
      </svg>
    </div>
  );

  const PixelLoader = () => (
    <div className={`${spinnerSize} ${className} grid grid-cols-3 gap-1`}>
      {[...Array(9)].map((_, i) => (
        <div 
          key={i}
          className="bg-red-500 aspect-square animate-pulse"
          style={{
            animationDelay: `${i * 0.1}s`,
            animationDuration: '1s'
          }}
        />
      ))}
    </div>
  );

  const renderLoader = () => {
    switch (variant) {
      case 'controller':
        return <ControllerLoader />;
      case 'dice':
        return <DiceLoader />;
      case 'pixel':
        return <PixelLoader />;
      default:
        return <GamepadLoader />;
    }
  };

  if (fullScreen) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-b from-[#121212] to-[#0a0a0a] flex flex-col items-center justify-center">
        {renderLoader()}
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center">
      {renderLoader()}
    </div>
  );
};

GameLoader.propTypes = {
  size: PropTypes.oneOf(['sm', 'md', 'lg', 'xl']),
  className: PropTypes.string,
  fullScreen: PropTypes.bool,
  variant: PropTypes.oneOf(['gamepad', 'controller', 'dice', 'pixel'])
};

export default GameLoader;
