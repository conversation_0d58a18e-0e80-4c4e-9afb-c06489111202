import { useState, useRef, useEffect } from 'react';
import { FaGlobe, FaChevronDown } from 'react-icons/fa';
import { useLanguage } from '../context/LanguageContext';

/**
 * Language Selector Component
 * Dropdown component for selecting the application language
 */
const LanguageSelector = () => {
  const { currentLanguage, availableLanguages, changeLanguage, t, isLoading } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle language selection
  const handleLanguageSelect = async (languageCode) => {
    if (languageCode !== currentLanguage && !isLoading) {
      await changeLanguage(languageCode);
      setIsOpen(false);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (event, languageCode) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleLanguageSelect(languageCode);
    } else if (event.key === 'Escape') {
      setIsOpen(false);
    }
  };

  const currentLangConfig = availableLanguages[currentLanguage];

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Language Selector Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-800 hover:bg-gray-700 text-white transition-all duration-300 border border-gray-600 hover:border-gray-500"
        aria-label={t('language.selector.title')}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        disabled={isLoading}
      >
        {/* Globe Icon */}
        <FaGlobe className="text-sm text-gray-300" />
        
        {/* Current Language Flag and Code */}
        <span className="text-lg" role="img" aria-label={currentLangConfig?.name}>
          {currentLangConfig?.flag}
        </span>
        <span className="text-sm font-medium uppercase hidden sm:inline">
          {currentLanguage}
        </span>
        
        {/* Dropdown Arrow */}
        <FaChevronDown 
          className={`text-xs text-gray-400 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`} 
        />
        
        {/* Loading Indicator */}
        {isLoading && (
          <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin" />
        )}
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-600 rounded-lg shadow-xl z-50 overflow-hidden">
          {/* Dropdown Header */}
          <div className="px-4 py-2 bg-gray-700 border-b border-gray-600">
            <p className="text-xs text-gray-300 font-medium">
              {t('language.selector.title')}
            </p>
          </div>
          
          {/* Language Options */}
          <div role="listbox" className="py-1">
            {Object.entries(availableLanguages).map(([langCode, langConfig]) => {
              const isSelected = langCode === currentLanguage;
              
              return (
                <button
                  key={langCode}
                  role="option"
                  aria-selected={isSelected}
                  onClick={() => handleLanguageSelect(langCode)}
                  onKeyDown={(e) => handleKeyDown(e, langCode)}
                  disabled={isLoading}
                  className={`w-full flex items-center space-x-3 px-4 py-3 text-left transition-colors duration-200 ${
                    isSelected
                      ? 'bg-gradient-to-r from-[#f44336] to-[#ff9800] text-white'
                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  } ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  {/* Flag */}
                  <span className="text-lg" role="img" aria-label={langConfig.name}>
                    {langConfig.flag}
                  </span>
                  
                  {/* Language Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <span className="font-medium truncate">
                        {langConfig.nativeName}
                      </span>
                      <span className="text-xs uppercase font-mono ml-2 opacity-75">
                        {langCode}
                      </span>
                    </div>
                    <div className="text-xs opacity-75 truncate">
                      {langConfig.name}
                    </div>
                  </div>
                  
                  {/* Selected Indicator */}
                  {isSelected && (
                    <div className="w-2 h-2 bg-white rounded-full flex-shrink-0" />
                  )}
                </button>
              );
            })}
          </div>
          
          {/* Footer with current selection info */}
          <div className="px-4 py-2 bg-gray-700 border-t border-gray-600">
            <p className="text-xs text-gray-400">
              {t('language.selector.current')}: {currentLangConfig?.nativeName}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default LanguageSelector;
