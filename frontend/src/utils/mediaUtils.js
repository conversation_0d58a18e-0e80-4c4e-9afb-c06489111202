/**
 * Media URL Utilities
 * Handles URL transformations for different environments (development vs production)
 * Ensures proper S3 bucket access when in production
 */

import { ENV_CONFIG } from '../config/env';

/**
 * Transform a media URL to the correct format based on environment
 * Works with both local uploads and S3 bucket URLs
 * 
 * @param {string} url - The original URL to transform
 * @returns {string} The transformed URL appropriate for the current environment
 */
export const getMediaUrl = (url) => {
  if (!url) return '';
  
  // Already an absolute URL (external resource)
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // If in production, use S3 bucket URL
  if (ENV_CONFIG.IS_PRODUCTION) {
    const s3BaseUrl = ENV_CONFIG.S3_URL || 'https://indierepo.com.s3.amazonaws.com';
    
    // Remove any leading slashes
    const cleanPath = url.replace(/^\/+/, '');
    
    // If the URL already contains 'uploads/', don't add it again
    if (cleanPath.startsWith('uploads/')) {
      return `${s3BaseUrl}/${cleanPath}`;
    }
    
    return `${s3BaseUrl}/uploads/${cleanPath}`;
  } 
  
  // In development, use local API server
  const baseUrl = ENV_CONFIG.BASE_URL || 'http://localhost:3000';
  
  // Handle URLs that might already have /uploads in them
  if (url.startsWith('/uploads/')) {
    return `${baseUrl}${url}`;
  }
  
  return `${baseUrl}/uploads/${url}`;
};

/**
 * Determine if the URL should be handled with S3 logic
 * @param {string} url - URL to check
 * @returns {boolean} - True if S3 handling is needed
 */
export const isS3Url = (url) => {
  if (!url) return false;
  return ENV_CONFIG.IS_PRODUCTION || url.includes('s3.amazonaws.com');
};

/**
 * Handle image load errors by providing fallback URLs
 * @param {Event} event - The error event from img tag
 * @param {string} fallbackUrl - Fallback URL to use
 */
export const handleImageError = (event, fallbackUrl) => {
  if (event.target) {
    event.target.src = fallbackUrl || '/placeholders/image-not-found.jpg';
  }
};

/**
 * Default avatars configuration
 */
const DEFAULT_AVATARS = [
  {
    id: 1,
    filename: '1.png',
    url: 'https://indierepo.s3.us-east-1.amazonaws.com/avatar/1.png',
    name: 'Avatar 1'
  },
  {
    id: 2,
    filename: '2.png',
    url: 'https://indierepo.s3.us-east-1.amazonaws.com/avatar/2.png',
    name: 'Avatar 2'
  }
];

/**
 * Get a consistent default avatar based on user ID
 * @param {number|string} userId - User ID for consistent avatar selection
 * @returns {string} - Default avatar URL
 */
export const getConsistentDefaultAvatar = (userId) => {
  if (!userId) return DEFAULT_AVATARS[0].url;

  // Use user ID to consistently select the same avatar
  const avatarIndex = parseInt(userId) % DEFAULT_AVATARS.length;
  return DEFAULT_AVATARS[avatarIndex].url;
};

/**
 * Get the appropriate avatar URL with fallback to consistent default
 * @param {string} avatarUrl - User's avatar URL from database
 * @param {number|string} userId - User ID for consistent default avatar fallback
 * @returns {string} - Avatar URL or consistent default
 */
export const getAvatarUrl = (avatarUrl, userId = null) => {
  // If avatar URL exists and is one of our default avatars, use it
  if (avatarUrl && (avatarUrl.includes('/avatar/1.png') || avatarUrl.includes('/avatar/2.png'))) {
    return getMediaUrl(avatarUrl);
  }

  // For any other case (social media images, no avatar, etc.), use consistent default
  return userId ? getConsistentDefaultAvatar(userId) : DEFAULT_AVATARS[0].url;
};

/**
 * Get the appropriate game cover image URL with fallback
 * @param {string} coverImageUrl - Game cover image URL
 * @returns {string} - Processed cover image URL
 */
export const getGameCoverUrl = (coverImageUrl) => {
  return coverImageUrl ? getMediaUrl(coverImageUrl) : '/placeholders/game-cover.jpg';
};

export default {
  getMediaUrl,
  isS3Url,
  handleImageError,
  getAvatarUrl,
  getConsistentDefaultAvatar,
  getGameCoverUrl
};
