/**
 * Utility functions for tracking game play history
 */

const RECENTLY_PLAYED_KEY = 'recentlyPlayed';
const MAX_RECENTLY_PLAYED = 50; // Maximum number of games to keep in history

/**
 * Add a game to recently played list
 * @param {Object} game - Game object with id, title, description, etc.
 */
export const addToRecentlyPlayed = (game) => {
  try {
    // Get existing recently played games
    const existing = getRecentlyPlayed();
    
    // Remove the game if it already exists (to avoid duplicates)
    const filtered = existing.filter(g => g.id !== game.id);
    
    // Add the game to the beginning of the list with current timestamp
    const gameWithTimestamp = {
      ...game,
      lastPlayed: new Date().toISOString(),
      playTime: 0 // Initialize play time
    };
    
    const updated = [gameWithTimestamp, ...filtered];
    
    // Keep only the most recent games (limit the list size)
    const limited = updated.slice(0, MAX_RECENTLY_PLAYED);
    
    // Save to localStorage
    localStorage.setItem(RECENTLY_PLAYED_KEY, JSON.stringify(limited));
    
    return limited;
  } catch (error) {
    console.error('Error adding game to recently played:', error);
    return [];
  }
};

/**
 * Get recently played games from localStorage
 * @returns {Array} Array of recently played games
 */
export const getRecentlyPlayed = () => {
  try {
    const stored = localStorage.getItem(RECENTLY_PLAYED_KEY);
    if (stored) {
      return JSON.parse(stored);
    }
    return [];
  } catch (error) {
    console.error('Error getting recently played games:', error);
    return [];
  }
};

/**
 * Remove a game from recently played list
 * @param {string|number} gameId - ID of the game to remove
 */
export const removeFromRecentlyPlayed = (gameId) => {
  try {
    const existing = getRecentlyPlayed();
    const filtered = existing.filter(g => g.id !== gameId);
    localStorage.setItem(RECENTLY_PLAYED_KEY, JSON.stringify(filtered));
    return filtered;
  } catch (error) {
    console.error('Error removing game from recently played:', error);
    return getRecentlyPlayed();
  }
};

/**
 * Clear all recently played games
 */
export const clearRecentlyPlayed = () => {
  try {
    localStorage.removeItem(RECENTLY_PLAYED_KEY);
    return [];
  } catch (error) {
    console.error('Error clearing recently played games:', error);
    return [];
  }
};

/**
 * Update play time for a game
 * @param {string|number} gameId - ID of the game
 * @param {number} additionalTime - Additional play time in seconds
 */
export const updatePlayTime = (gameId, additionalTime) => {
  try {
    const existing = getRecentlyPlayed();
    const updated = existing.map(game => {
      if (game.id === gameId) {
        return {
          ...game,
          playTime: (game.playTime || 0) + additionalTime,
          lastPlayed: new Date().toISOString()
        };
      }
      return game;
    });
    
    localStorage.setItem(RECENTLY_PLAYED_KEY, JSON.stringify(updated));
    return updated;
  } catch (error) {
    console.error('Error updating play time:', error);
    return getRecentlyPlayed();
  }
};

/**
 * Check if a game is in recently played list
 * @param {string|number} gameId - ID of the game to check
 * @returns {boolean} True if game is in recently played list
 */
export const isInRecentlyPlayed = (gameId) => {
  try {
    const existing = getRecentlyPlayed();
    return existing.some(g => g.id === gameId);
  } catch (error) {
    console.error('Error checking recently played:', error);
    return false;
  }
};

/**
 * Get recently played games count
 * @returns {number} Number of recently played games
 */
export const getRecentlyPlayedCount = () => {
  try {
    const existing = getRecentlyPlayed();
    return existing.length;
  } catch (error) {
    console.error('Error getting recently played count:', error);
    return 0;
  }
};

/**
 * Format play time for display
 * @param {number} seconds - Play time in seconds
 * @returns {string} Formatted play time string
 */
export const formatPlayTime = (seconds) => {
  if (!seconds || seconds < 60) {
    return 'Less than a minute';
  }
  
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  }
  
  return `${minutes}m`;
};

/**
 * Format last played time for display
 * @param {string} timestamp - ISO timestamp string
 * @returns {string} Formatted time string
 */
export const formatLastPlayed = (timestamp) => {
  try {
    const now = new Date();
    const played = new Date(timestamp);
    const diffInMinutes = Math.floor((now - played) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} days ago`;
    
    return played.toLocaleDateString();
  } catch (error) {
    console.error('Error formatting last played time:', error);
    return 'Unknown';
  }
};
