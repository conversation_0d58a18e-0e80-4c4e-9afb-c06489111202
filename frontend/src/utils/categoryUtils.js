import gameCategories from '../data/gameCategories.json';

/**
 * Get all available game categories
 * @returns {Array} Array of category objects
 */
export const getGameCategories = () => {
  return gameCategories.categories;
};

/**
 * Get category by value
 * @param {string} value - Category value (e.g., 'action')
 * @returns {Object|null} Category object or null if not found
 */
export const getCategoryByValue = (value) => {
  return gameCategories.categories.find(category => category.value === value) || null;
};

/**
 * Get category by slug
 * @param {string} slug - Category slug (e.g., 'action')
 * @returns {Object|null} Category object or null if not found
 */
export const getCategoryBySlug = (slug) => {
  return gameCategories.categories.find(category => category.slug === slug) || null;
};

/**
 * Get all category values as an array
 * @returns {Array} Array of category values
 */
export const getCategoryValues = () => {
  return gameCategories.categories.map(category => category.value);
};

/**
 * Get all category labels as an array
 * @returns {Array} Array of category labels
 */
export const getCategoryLabels = () => {
  return gameCategories.categories.map(category => category.label);
};

/**
 * Get all category slugs as an array
 * @returns {Array} Array of category slugs
 */
export const getCategorySlugs = () => {
  return gameCategories.categories.map(category => category.slug);
};

/**
 * Check if a category value is valid
 * @param {string} value - Category value to check
 * @returns {boolean} True if valid, false otherwise
 */
export const isValidCategoryValue = (value) => {
  return gameCategories.categories.some(category => category.value === value);
};

/**
 * Check if a category slug is valid
 * @param {string} slug - Category slug to check
 * @returns {boolean} True if valid, false otherwise
 */
export const isValidCategorySlug = (slug) => {
  return gameCategories.categories.some(category => category.slug === slug);
};

/**
 * Get formatted category options for select dropdowns
 * @returns {Array} Array of objects with value and label properties
 */
export const getCategoryOptions = () => {
  return gameCategories.categories.map(category => ({
    value: category.value,
    label: category.label
  }));
};
