/**
 * Image utilities for secure URL handling
 */

import { useState, useEffect } from 'react';

// Configuration
const CONFIG = {
  // Direct S3 URLs (reverted approach)
  S3_BASE: 'https://indierepo.s3.us-east-1.amazonaws.com',
  // Future CDN endpoint
  CDN_BASE: 'https://cdn.indierepo.com', // When you set up CloudFront
};

/**
 * Get the direct S3 URL for an image
 * @param {string} imagePath - Image path or existing URL
 * @returns {string} - Direct S3 URL
 */
export const getSecureImageUrl = (imagePath) => {
  if (!imagePath) return '';
  
  // If it's already a full URL (S3 or other), return as-is
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }
  
  // If it's a relative path, construct S3 URL
  if (imagePath.startsWith('/')) {
    // Remove leading slash and construct S3 URL
    return `${CONFIG.S3_BASE}${imagePath}`;
  }
  
  // If it's a path without leading slash, add it and construct S3 URL
  return `${CONFIG.S3_BASE}/${imagePath}`;
};

/**
 * Get optimized image URL with CDN
 * @param {string} imageUrl - Original image URL
 * @param {Object} options - Optimization options
 * @returns {string} - Optimized image URL
 */
export const getOptimizedImageUrl = (imageUrl, options = {}) => {
  const {
    width,
    height,
    quality = 80,
    format = 'webp',
    useCDN = true
  } = options;
  
  // If CDN is available and enabled, use it
  if (useCDN && CONFIG.CDN_BASE !== CONFIG.S3_BASE) {
    // Replace S3 base with CDN base
    const cdnUrl = imageUrl.replace(CONFIG.S3_BASE, CONFIG.CDN_BASE);
    
    // Add query parameters for image optimization (if your CDN supports it)
    const params = new URLSearchParams();
    if (width) params.append('w', width);
    if (height) params.append('h', height);
    if (quality) params.append('q', quality);
    if (format) params.append('f', format);
    
    const queryString = params.toString();
    return queryString ? `${cdnUrl}?${queryString}` : cdnUrl;
  }
  
  // Fallback to secure proxy
  return getSecureImageUrl(imageUrl);
};

/**
 * Check if URL is from a trusted source
 * @param {string} url - URL to check
 * @returns {boolean} - Whether URL is trusted
 */
export const isTrustedImageUrl = (url) => {
  const trustedDomains = [
    'indierepo.com',
    'cdn.indierepo.com',
    window.location.hostname // Your current domain
  ];
  
  try {
    const urlObj = new URL(url);
    return trustedDomains.some(domain => 
      urlObj.hostname === domain || urlObj.hostname.endsWith(`.${domain}`)
    );
  } catch {
    return false;
  }
};

/**
 * Preload images for better performance
 * @param {Array<string>} imageUrls - Array of image URLs
 */
export const preloadImages = (imageUrls) => {
  imageUrls.forEach(url => {
    const img = new Image();
    img.src = getOptimizedImageUrl(url);
  });
};

/**
 * React hook for secure image loading
 * @param {string} imageUrl - Original image URL
 * @param {Object} options - Options for optimization
 * @returns {Object} - { src, isLoading, error }
 */
export const useSecureImage = (imageUrl, options = {}) => {
  const [state, setState] = useState({
    src: '',
    isLoading: true,
    error: null
  });
  
  useEffect(() => {
    if (!imageUrl) {
      setState({ src: '', isLoading: false, error: 'No image URL provided' });
      return;
    }
    
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    const secureUrl = getOptimizedImageUrl(imageUrl, options);
    
    // Test if image loads successfully
    const img = new Image();
    img.onload = () => {
      setState({ src: secureUrl, isLoading: false, error: null });
    };
    img.onerror = () => {
      setState({ src: '', isLoading: false, error: 'Failed to load image' });
    };
    img.src = secureUrl;
    
  }, [imageUrl, options.width, options.height, options.quality, options.format, options.useCDN]);
  
  return state;
};

// Export configuration for use in other files
export { CONFIG };
