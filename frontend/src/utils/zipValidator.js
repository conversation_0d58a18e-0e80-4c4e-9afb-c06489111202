import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import { UPLOAD_CONFIG } from '../config/uploadConfig';

/**
 * Validates a ZIP file to ensure it contains the required files for a web game
 * @param {File} file - The ZIP file to validate
 * @returns {Promise<{isValid: boolean, errors: string[], hasIndexHtml: boolean, hasCompressedFiles: boolean}>}
 */
export const validateZipFile = async (file) => {
  const result = {
    isValid: false,
    errors: [],
    hasIndexHtml: false,
    hasCompressedFiles: false,
    fileList: []
  };

  try {
    // Load the ZIP file
    const zip = new JSZip();
    const zipContent = await zip.loadAsync(file);
    
    // Get all file names in the ZIP
    const fileNames = Object.keys(zipContent.files);
    result.fileList = fileNames;
    
    // Check for index.html in root folder
    const hasIndexInRoot = fileNames.some(fileName => {
      const file = zipContent.files[fileName];
      return !file.dir && fileName.toLowerCase() === 'index.html';
    });
    
    if (!hasIndexInRoot) {
      result.errors.push('ZIP file must contain an index.html file in the root folder');
    } else {
      result.hasIndexHtml = true;
    }
    
    // Check for compressed files (.gzip, .gz, .br)
    const hasCompressedFiles = fileNames.some(fileName => {
      const lowerFileName = fileName.toLowerCase();
      return UPLOAD_CONFIG.REQUIRED_ZIP_FILES.COMPRESSION_EXTENSIONS.some(ext => 
        lowerFileName.endsWith(ext)
      );
    });
    
    if (!hasCompressedFiles) {
      result.errors.push('ZIP file should contain compressed files (.gzip, .gz, or .br) for optimal loading');
      // This is a warning, not a blocking error
    } else {
      result.hasCompressedFiles = true;
    }
    
    // Check for common web game files
    const hasWebGameFiles = fileNames.some(fileName => {
      const lowerFileName = fileName.toLowerCase();
      return lowerFileName.endsWith('.js') || 
             lowerFileName.endsWith('.wasm') || 
             lowerFileName.endsWith('.data') ||
             lowerFileName.endsWith('.unity3d') ||
             lowerFileName.endsWith('.unityweb');
    });
    
    if (!hasWebGameFiles) {
      result.errors.push('ZIP file should contain web game files (.js, .wasm, .data, etc.)');
    }
    
    // Check file structure - warn if files are nested too deeply
    const hasDeepNesting = fileNames.some(fileName => {
      const pathParts = fileName.split('/');
      return pathParts.length > 3; // More than 2 levels deep
    });
    
    if (hasDeepNesting) {
      result.errors.push('Warning: Some files are nested deeply. Consider flattening the folder structure for better organization');
    }
    
    // Validation passes if we have index.html (compressed files are recommended but not required)
    result.isValid = result.hasIndexHtml;
    
  } catch (error) {
    console.error('Error validating ZIP file:', error);
    result.errors.push('Failed to read ZIP file. Please ensure the file is a valid ZIP archive.');
  }
  
  return result;
};

/**
 * Gets a preview of the ZIP file contents
 * @param {File} file - The ZIP file to preview
 * @returns {Promise<{files: Array, totalSize: number, error: string|null}>}
 */
export const getZipPreview = async (file) => {
  try {
    const zip = new JSZip();
    const zipContent = await zip.loadAsync(file);
    
    const files = [];
    let totalSize = 0;
    
    Object.keys(zipContent.files).forEach(fileName => {
      const zipFile = zipContent.files[fileName];
      if (!zipFile.dir) {
        files.push({
          name: fileName,
          size: zipFile._data ? zipFile._data.uncompressedSize : 0,
          isCompressed: UPLOAD_CONFIG.REQUIRED_ZIP_FILES.COMPRESSION_EXTENSIONS.some(ext => 
            fileName.toLowerCase().endsWith(ext)
          )
        });
        totalSize += zipFile._data ? zipFile._data.uncompressedSize : 0;
      }
    });
    
    return {
      files: files.sort((a, b) => a.name.localeCompare(b.name)),
      totalSize,
      error: null
    };
    
  } catch (error) {
    console.error('Error previewing ZIP file:', error);
    return {
      files: [],
      totalSize: 0,
      error: 'Failed to read ZIP file contents'
    };
  }
};
