import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import PropTypes from 'prop-types';
import { AuthProvider } from './context/AuthContext';
import { LanguageProvider } from './context/LanguageContext';
import { SidebarProvider } from './context/SidebarContext';
import { GoogleOAuthProvider } from '@react-oauth/google';

// Pages
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import SearchPage from './pages/SearchPage';
import GamePage from './pages/GamePage';
import AboutPage from './pages/AboutPage';
import UploadGamePage from './pages/UploadGamePage';
import NotFoundPage from './pages/NotFoundPage';
import DiscordCallback from './pages/DiscordCallback';
import ProfilePage from './pages/ProfilePage';
import UserProfilePage from './pages/UserProfilePage';
import CategoryPage from './pages/CategoryPage';
import RecentlyPlayedPage from './pages/RecentlyPlayedPage';

import Header from './components/Header';
import Footer from './components/Footer';
import Sidebar from './components/Sidebar';
import { useSidebar } from './context/SidebarContext';

// Layout wrapper component to handle sidebar
const LayoutWrapper = ({ children }) => {
  const { isSidebarOpen, toggleSidebar, isInitialized } = useSidebar();

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Header />
      <div className="flex flex-1">
        <Sidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />
        <main className={`flex-1 ${isInitialized ? 'transition-all duration-300' : ''} ${isSidebarOpen ? 'lg:pl-64' : 'lg:pl-0'} overflow-x-hidden`}>
          {children}
        </main>
      </div>
      <Footer />
    </div>
  );
};

LayoutWrapper.propTypes = {
  children: PropTypes.node.isRequired
};

function App() {
  return (
    <GoogleOAuthProvider clientId={import.meta.env.VITE_GOOGLE_CLIENT_ID}>
      <Router>
        <LanguageProvider>
          <AuthProvider>
            <SidebarProvider>
              <LayoutWrapper>
                <Routes>
                  <Route path="/" element={<HomePage />} />
                  <Route path="/login" element={<LoginPage />} />
                  <Route path="/register" element={<RegisterPage />} />
                  <Route path="/search" element={<SearchPage />} />
                  <Route path="/about" element={<AboutPage />} />
                  <Route path="/upload-game" element={<UploadGamePage />} />
                  <Route path="/auth/discord/callback" element={<DiscordCallback />} />
                  <Route path="/profile" element={<ProfilePage />} />
                  <Route path="/user/:userId" element={<UserProfilePage />} />
                  <Route path="/game/:id" element={<GamePage />} />

                  {/* Navigation Routes */}
                  <Route path="/recently-played" element={<RecentlyPlayedPage />} />
                  <Route path="/new" element={<CategoryPage />} />
                  <Route path="/trending" element={<CategoryPage />} />
                  <Route path="/updated" element={<CategoryPage />} />
                  <Route path="/originals" element={<CategoryPage />} />
                  <Route path="/multiplayer" element={<CategoryPage />} />

                  {/* Category Routes */}
                  <Route path="/category/2-player" element={<CategoryPage />} />
                  <Route path="/category/action" element={<CategoryPage />} />
                  <Route path="/category/adventure" element={<CategoryPage />} />
                  <Route path="/category/basketball" element={<CategoryPage />} />
                  <Route path="/category/beauty" element={<CategoryPage />} />
                  <Route path="/category/bike" element={<CategoryPage />} />
                  <Route path="/category/car" element={<CategoryPage />} />
                  <Route path="/category/card" element={<CategoryPage />} />
                  <Route path="/category/casual" element={<CategoryPage />} />
                  <Route path="/category/clicker" element={<CategoryPage />} />
                  <Route path="/category/controller" element={<CategoryPage />} />
                  <Route path="/category/dress-up" element={<CategoryPage />} />
                  <Route path="/category/driving" element={<CategoryPage />} />
                  <Route path="/category/escape" element={<CategoryPage />} />
                  <Route path="/category/flash" element={<CategoryPage />} />
                  <Route path="/category/fps" element={<CategoryPage />} />
                  <Route path="/category/horror" element={<CategoryPage />} />

                  {/* Game by slug route - now at root level */}
                  <Route path="/:gameSlug" element={<GamePage usePath={true} />} />
                  <Route path="*" element={<NotFoundPage />} />
                </Routes>
              </LayoutWrapper>
            </SidebarProvider>
          </AuthProvider>
        </LanguageProvider>
      </Router>
    </GoogleOAuthProvider>
  );
}

export default App;
