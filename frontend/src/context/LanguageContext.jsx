import { createContext, useState, useContext, useEffect } from 'react';
import PropTypes from 'prop-types';
import GameLoader from '../components/GameLoader';

// Translation loader function that works better with Vite
const loadTranslationFile = async (languageCode) => {
  try {
    switch (languageCode) {
      case 'en':
        return (await import('../translations/en.json')).default;
      case 'es':
        return (await import('../translations/es.json')).default;
      case 'pt':
        return (await import('../translations/pt.json')).default;
      default:
        return (await import('../translations/en.json')).default;
    }
  } catch (error) {
    console.error(`Failed to load translation file for ${languageCode}:`, error);
    // Fallback to English
    try {
      return (await import('../translations/en.json')).default;
    } catch (fallbackError) {
      console.error('Failed to load fallback English translations:', fallbackError);
      return {};
    }
  }
};

// Create context
const LanguageContext = createContext();

// Available languages configuration
export const AVAILABLE_LANGUAGES = {
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    countries: ['US', 'GB', 'CA', 'AU', 'NZ', 'IE', 'ZA']
  },
  es: {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    flag: '🇪🇸',
    countries: ['ES', 'MX', 'AR', 'CO', 'PE', 'VE', 'CL', 'EC', 'GT', 'CU', 'BO', 'DO', 'HN', 'PY', 'SV', 'NI', 'CR', 'PA', 'UY', 'PR']
  },
  pt: {
    code: 'pt',
    name: 'Portuguese',
    nativeName: 'Português',
    flag: '🇧🇷',
    countries: ['BR', 'PT', 'AO', 'MZ', 'GW', 'CV', 'ST', 'TL']
  }
};

// Default language
const DEFAULT_LANGUAGE = 'en';

// Storage key for language preference
const LANGUAGE_STORAGE_KEY = 'indierepo_language';

export const LanguageProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState(DEFAULT_LANGUAGE);
  const [isLoading, setIsLoading] = useState(true);
  const [translations, setTranslations] = useState({});

  // Load translations for a specific language using dynamic imports
  const loadTranslations = async (languageCode) => {
    try {
      const translations = await loadTranslationFile(languageCode);
      return translations;
    } catch (error) {
      console.error(`❌ Failed to load translations for ${languageCode}:`, error);
      // Try to load English as fallback
      try {
        const fallbackTranslations = await loadTranslationFile('en');
        return fallbackTranslations;
      } catch (fallbackError) {
        console.error(`❌ Failed to load fallback translations:`, fallbackError);
        return {};
      }
    }
  };

  // Get user's country from geolocation API
  const getUserCountry = async () => {
    try {
      // Try multiple geolocation services for better reliability
      const services = [
        'https://ipapi.co/country_code/',
        'https://api.country.is/',
        'https://ipinfo.io/country'
      ];

      for (const service of services) {
        try {
          const response = await fetch(service);
          if (response.ok) {
            let countryCode;
            if (service.includes('country.is')) {
              const data = await response.json();
              countryCode = data.country;
            } else {
              countryCode = await response.text();
            }
            return countryCode.trim().toUpperCase();
          }
        } catch (error) {
          console.warn(`Failed to get country from ${service}:`, error);
          continue;
        }
      }
      
      return null;
    } catch (error) {
      console.warn('Failed to detect user country:', error);
      return null;
    }
  };

  // Map country code to language
  const getLanguageFromCountry = (countryCode) => {
    if (!countryCode) return DEFAULT_LANGUAGE;

    for (const [langCode, langConfig] of Object.entries(AVAILABLE_LANGUAGES)) {
      if (langConfig.countries.includes(countryCode)) {
        return langCode;
      }
    }
    
    return DEFAULT_LANGUAGE;
  };

  // Initialize language on component mount
  useEffect(() => {
    const initializeLanguage = async () => {
      try {

        // Check if user has a saved language preference
        const savedLanguage = localStorage.getItem(LANGUAGE_STORAGE_KEY);

        let selectedLanguage = DEFAULT_LANGUAGE;

        if (savedLanguage && AVAILABLE_LANGUAGES[savedLanguage]) {
          // Use saved language preference
          selectedLanguage = savedLanguage;
        } else {
          // Detect language from user's country
          const userCountry = await getUserCountry();
          selectedLanguage = getLanguageFromCountry(userCountry);
        }

        // Load translations for the selected language
        const languageTranslations = await loadTranslations(selectedLanguage);

        setCurrentLanguage(selectedLanguage);
        setTranslations(languageTranslations);

        // Save the detected/selected language
        localStorage.setItem(LANGUAGE_STORAGE_KEY, selectedLanguage);

      } catch (error) {
        console.error('❌ Error initializing language:', error);
        // Fallback to default language
        try {
          const fallbackTranslations = await loadTranslations(DEFAULT_LANGUAGE);
          setCurrentLanguage(DEFAULT_LANGUAGE);
          setTranslations(fallbackTranslations);
        } catch (fallbackError) {
          console.error('❌ Failed to load fallback translations:', fallbackError);
          setTranslations({});
        }
      } finally {
        setIsLoading(false);
      }
    };

    initializeLanguage();
  }, []);

  // Change language function
  const changeLanguage = async (languageCode) => {
    if (!AVAILABLE_LANGUAGES[languageCode]) {
      console.warn(`Language ${languageCode} is not available`);
      return;
    }

    try {
      setIsLoading(true);

      // Load new translations
      const newTranslations = await loadTranslations(languageCode);

      // Update state
      setCurrentLanguage(languageCode);
      setTranslations(newTranslations);

      // Save preference
      localStorage.setItem(LANGUAGE_STORAGE_KEY, languageCode);

    } catch (error) {
      console.error('❌ Error changing language:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Translation function
  const t = (key, params = {}) => {
    if (!key) return '';

    // Navigate through nested object using dot notation
    const keys = key.split('.');
    let value = translations;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        // Return key if translation not found (useful for development)
        console.warn(`Translation missing for key: ${key} in language: ${currentLanguage}`);
        return key;
      }
    }

    // Handle string interpolation
    if (typeof value === 'string' && Object.keys(params).length > 0) {
      return value.replace(/\{\{(\w+)\}\}/g, (match, paramKey) => {
        return params[paramKey] || match;
      });
    }

    return value || key;
  };

  const contextValue = {
    currentLanguage,
    availableLanguages: AVAILABLE_LANGUAGES,
    isLoading,
    changeLanguage,
    t,
    translations
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {isLoading ? (
        <GameLoader fullScreen={true} size="xl" variant="pixel" />
      ) : (
        children
      )}
    </LanguageContext.Provider>
  );
};

LanguageProvider.propTypes = {
  children: PropTypes.node.isRequired
};

// Hook for using the language context
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export default LanguageContext;
