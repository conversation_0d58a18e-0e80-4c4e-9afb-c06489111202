import { createContext, useState, useContext, useEffect } from 'react';
import PropTypes from 'prop-types';

// Create context
const SidebarContext = createContext();

export const SidebarProvider = ({ children }) => {
  // Sidebar state - open by default on desktop, closed on mobile
  const [isSidebarOpen, setIsSidebarOpen] = useState(() => {
    // Initialize immediately based on screen size to avoid animation on load
    return window.innerWidth >= 1024;
  });

  // Add initialized state to prevent transition on first render
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize sidebar state based on screen size
  useEffect(() => {
    // Enable transitions after mount
    setIsInitialized(true);
    
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsSidebarOpen(true); // Open by default on desktop
      } else {
        setIsSidebarOpen(false); // Closed by default on mobile
      }
    };

    // Add resize listener (no need to call handleResize here since we already set initial state)
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Toggle sidebar function
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const contextValue = {
    isSidebarOpen,
    setIsSidebarOpen,
    toggleSidebar,
    isInitialized
  };

  return (
    <SidebarContext.Provider value={contextValue}>
      {children}
    </SidebarContext.Provider>
  );
};

SidebarProvider.propTypes = {
  children: PropTypes.node.isRequired
};

// Hook for using the sidebar context
export const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
};
