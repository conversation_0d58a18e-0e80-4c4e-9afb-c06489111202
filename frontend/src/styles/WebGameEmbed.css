.web-game-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 75%; /* Set aspect ratio to 4:3 to give more vertical space */
  min-height: 600px; /* Increase minimum height from 400px to 600px */
  background-color: #111;
  border-radius: 8px;
  overflow: hidden;
}

.web-game-frame {
  width: 100%;
  height: 100%;
  border: none;
  position: absolute;
  top: 0;
  left: 0;
}

.web-game-loading,
.web-game-error,
.web-game-not-available {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  z-index: 1;
}

.web-game-loading .loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.web-game-error svg,
.web-game-not-available svg {
  font-size: 48px;
  margin-bottom: 16px;
  color: #ff5555;
}

.play-external-btn {
  display: inline-block;
  margin-top: 16px;
  padding: 8px 16px;
  background-color: #4a90e2;
  color: white;
  border-radius: 4px;
  text-decoration: none;
  font-weight: bold;
  transition: background-color 0.2s;
}

.play-external-btn:hover {
  background-color: #3a80d2;
}

.web-game-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background-color: #4a90e2;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 12px;
}

.web-game-play-btn-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.web-game-play-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #4a90e2;
  color: white;
  padding: 12px 24px;
  border-radius: 30px;
  font-weight: bold;
  text-decoration: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: transform 0.2s, background-color 0.2s;
}

.web-game-play-btn:hover {
  background-color: #3a80d2;
  transform: translateY(-2px);
}

.web-game-play-btn svg {
  font-size: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .web-game-container {
    min-height: 450px; /* Increase from 300px to 450px */
    padding-bottom: 85%; /* Increase aspect ratio on smaller screens */
  }
}

@media (max-width: 480px) {
  .web-game-container {
    min-height: 350px; /* Increase from 250px to 350px */
    padding-bottom: 100%; /* Square aspect ratio on very small screens */
  }
}

/* Debug information styling */
.web-game-debug, 
.web-game-debug-url {
  font-size: 12px;
  max-width: 90%;
  overflow-wrap: break-word;
  opacity: 0.8;
  margin-top: 8px;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
}
