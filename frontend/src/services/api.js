import axios from 'axios';
import { API_URL as ENV_API_URL } from '../config/env.js';

// Get API base URL with domain matching logic
const getApiBaseUrl = () => {
  // In browser environment
  if (typeof window !== 'undefined') {
    const currentHostname = window.location.hostname;
    
    // In production environment (note we don't specify port as we're using Nginx)
    // This handles both your domain and any other production domains
    if (import.meta.env.PROD && currentHostname !== 'localhost' && currentHostname !== '127.0.0.1') {
      // For EC2 deployment with Nginx, we use relative URLs to avoid CORS issues
      return '/api';
    }
    
    // If we're on indierepo.com or any subdomain (including www)
    if (currentHostname === 'indierepo.com' || 
        currentHostname === 'www.indierepo.com' || 
        currentHostname.endsWith('.indierepo.com')) {
      // In production, always use relative URL to avoid mixed content issues
      // Nginx will handle the proxying to the backend
      return '/api';
    }
    
    // For localhost development 
    if (currentHostname === 'localhost' || currentHostname === '127.0.0.1') {
      return ENV_API_URL; // Use centralized configuration
    }
  }
  
  // Fallback to centralized env configuration
  return ENV_API_URL;
};

// Get the appropriate API URL
const API_URL = getApiBaseUrl();


// Create axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  withCredentials: true, // This is critical for sending/receiving cookies
  headers: {
    'Content-Type': 'application/json'
  }
});

// Request interceptor for authentication
api.interceptors.request.use(
  (config) => {

    
    return config;
  },
  (error) => {
    console.error('❌ API Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    // Return successful responses as-is
    return response;
  },
  (error) => {
    console.error('❌ Auth Error:', error.message);
    
        // Special handling for authentication errors
    if (error.response && error.response.status === 401) {
      // Authentication error - could redirect to login if needed
    }
    
    return Promise.reject(error);
  }
);

export default api;
