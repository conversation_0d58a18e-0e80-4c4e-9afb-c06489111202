import axios from 'axios';

import { API_URL } from '../config/env.js';

// Create an axios instance with correct CORS config
axios.create({
  baseURL: API_URL,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Handle Discord errors with better error information
const handleDiscordError = (error) => {
  console.error('Discord login error:', error);
  
  if (error.response) {
    console.error('Error response:', error.response.data);
    
    // Return the server's error message if available
    if (error.response.data && error.response.data.message) {
      throw error.response.data;
    }
    
    throw { 
      message: `Server error (${error.response.status})`, 
      status: error.response.status 
    };
  }
  
  if (error.request) {
    // The request was made but no response was received
    console.error('No response received from server');
    throw { message: 'No response from authentication service' };
  }
  
  // Something happened in setting up the request
  throw { message: 'Failed to connect to authentication service' };
};

// Track processed codes to prevent duplicates
const processedCodes = new Set();

// Exchange Discord code for token and user info
export const handleDiscordCallback = async (code) => {
  try {
    // Check if this code was already processed
    if (processedCodes.has(code)) {
      console.warn('Avoiding duplicate Discord auth request with same code');
      throw new Error('This authentication code has already been used');
    }
    
    // Mark this code as being processed
    processedCodes.add(code);
    

    
    if (!code || typeof code !== 'string' || code.length < 20) {
      throw new Error('Invalid Discord authorization code format');
    }
    
    // Create the redirect URI to match what the backend expects
    const redirectUri = import.meta.env.VITE_DISCORD_REDIRECT_URI || 
                       `${window.location.origin}/auth/discord/callback`;
    
    
    
    // Use standard axios instead of the custom instance to try to isolate CORS issues
    const response = await axios({
      method: 'post',
      url: `${API_URL}/auth/discord/callback`,
      data: { code, redirectUri },
      headers: { 'Content-Type': 'application/json' },
      // Don't use withCredentials for now to avoid CORS issues
      withCredentials: false
    });
    
    
    
    if (response.data && response.data.token && response.data.user) {
      // Store token in localStorage
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
      
      return {
        user: response.data.user,
        token: response.data.token
      };
    } else {
      throw new Error('Invalid response from server');
    }
  } catch (error) {
    console.error('Discord login error:', error);
    if (error.response?.data) {
      console.error('Error response:', error.response.data);
      throw error.response.data.message || 'Discord authentication failed';
    }
    throw error.message || 'Discord authentication failed';
  }
};

// Generate Discord OAuth URL with state parameter for security
export const getDiscordOAuthUrl = () => {
  const clientId = import.meta.env.VITE_DISCORD_CLIENT_ID;
  const redirectUri = encodeURIComponent(
    import.meta.env.VITE_DISCORD_REDIRECT_URI || `${window.location.origin}/auth/discord/callback`
  );
  const scope = encodeURIComponent('identify email');
  
  // Generate a random state value and store it in sessionStorage
  const state = Math.random().toString(36).substring(2, 15);
  sessionStorage.setItem('discordOAuthState', state);
  
  return `https://discord.com/api/oauth2/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}&state=${state}`;
};
