/**
 * Geolocation Service
 * Handles user location detection and language mapping
 */

// Country to language mapping
const COUNTRY_LANGUAGE_MAP = {
  // English-speaking countries
  'US': 'en', 'GB': 'en', 'CA': 'en', 'AU': 'en', 'NZ': 'en', 'IE': 'en', 'ZA': 'en',
  
  // Spanish-speaking countries
  'ES': 'es', 'MX': 'es', 'AR': 'es', 'CO': 'es', 'PE': 'es', 'VE': 'es', 
  'CL': 'es', 'EC': 'es', 'GT': 'es', 'CU': 'es', 'BO': 'es', 'DO': 'es', 
  'HN': 'es', 'PY': 'es', 'SV': 'es', 'NI': 'es', 'CR': 'es', 'PA': 'es', 
  'UY': 'es', 'PR': 'es',
  
  // Portuguese-speaking countries
  'BR': 'pt', 'PT': 'pt', 'AO': 'pt', 'MZ': 'pt', 'GW': 'pt', 'CV': 'pt', 
  'ST': 'pt', 'TL': 'pt'
};

// Default language fallback
const DEFAULT_LANGUAGE = 'en';

// Cache for geolocation results to avoid repeated API calls
let cachedCountryCode = null;
let cacheTimestamp = null;
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

/**
 * Get user's country code using multiple geolocation services
 * @returns {Promise<string|null>} Country code (ISO 3166-1 alpha-2) or null if detection fails
 */
export const getUserCountryCode = async () => {
  // Check cache first
  if (cachedCountryCode && cacheTimestamp && (Date.now() - cacheTimestamp < CACHE_DURATION)) {
    return cachedCountryCode;
  }

  // List of geolocation services (ordered by reliability and speed)
  const geolocationServices = [
    {
      name: 'ipapi.co',
      url: 'https://ipapi.co/country_code/',
      parseResponse: async (response) => {
        const text = await response.text();
        return text.trim().toUpperCase();
      }
    },
    {
      name: 'country.is',
      url: 'https://api.country.is/',
      parseResponse: async (response) => {
        const data = await response.json();
        return data.country?.toUpperCase();
      }
    },
    {
      name: 'ipinfo.io',
      url: 'https://ipinfo.io/country',
      parseResponse: async (response) => {
        const text = await response.text();
        return text.trim().toUpperCase();
      }
    },
    {
      name: 'ip-api.com',
      url: 'http://ip-api.com/json/?fields=countryCode',
      parseResponse: async (response) => {
        const data = await response.json();
        return data.countryCode?.toUpperCase();
      }
    }
  ];

  // Try each service until one succeeds
  for (const service of geolocationServices) {
    try {
      console.log(`Attempting to get country code from ${service.name}...`);
      
      const response = await fetch(service.url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json, text/plain, */*',
        },
        // Add timeout to prevent hanging requests
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const countryCode = await service.parseResponse(response);
      
      if (countryCode && typeof countryCode === 'string' && countryCode.length === 2) {
        console.log(`Successfully detected country: ${countryCode} from ${service.name}`);
        
        // Cache the result
        cachedCountryCode = countryCode;
        cacheTimestamp = Date.now();
        
        return countryCode;
      } else {
        throw new Error(`Invalid country code format: ${countryCode}`);
      }
      
    } catch (error) {
      console.warn(`Failed to get country from ${service.name}:`, error.message);
      continue; // Try next service
    }
  }

  console.warn('All geolocation services failed, unable to detect user country');
  return null;
};

/**
 * Map country code to appropriate language
 * @param {string|null} countryCode - ISO 3166-1 alpha-2 country code
 * @returns {string} Language code (ISO 639-1)
 */
export const getLanguageFromCountry = (countryCode) => {
  if (!countryCode || typeof countryCode !== 'string') {
    return DEFAULT_LANGUAGE;
  }

  const normalizedCountryCode = countryCode.toUpperCase();
  const language = COUNTRY_LANGUAGE_MAP[normalizedCountryCode];
  
  if (language) {
    console.log(`Mapped country ${normalizedCountryCode} to language ${language}`);
    return language;
  }

  console.log(`No language mapping found for country ${normalizedCountryCode}, using default: ${DEFAULT_LANGUAGE}`);
  return DEFAULT_LANGUAGE;
};

/**
 * Detect user's preferred language based on their location
 * @returns {Promise<string>} Language code (ISO 639-1)
 */
export const detectUserLanguage = async () => {
  try {
    const countryCode = await getUserCountryCode();
    return getLanguageFromCountry(countryCode);
  } catch (error) {
    console.error('Error detecting user language:', error);
    return DEFAULT_LANGUAGE;
  }
};

/**
 * Get browser's preferred language as fallback
 * @returns {string} Language code (ISO 639-1)
 */
export const getBrowserLanguage = () => {
  try {
    // Get browser language preference
    const browserLang = navigator.language || navigator.languages?.[0] || DEFAULT_LANGUAGE;
    
    // Extract language code (first 2 characters)
    const langCode = browserLang.substring(0, 2).toLowerCase();
    
    // Check if we support this language
    const supportedLanguages = ['en', 'es', 'pt'];
    if (supportedLanguages.includes(langCode)) {
      console.log(`Using browser language: ${langCode}`);
      return langCode;
    }
    
    console.log(`Browser language ${langCode} not supported, using default: ${DEFAULT_LANGUAGE}`);
    return DEFAULT_LANGUAGE;
  } catch (error) {
    console.warn('Error getting browser language:', error);
    return DEFAULT_LANGUAGE;
  }
};

/**
 * Comprehensive language detection with multiple fallbacks
 * @returns {Promise<string>} Language code (ISO 639-1)
 */
export const detectLanguageWithFallbacks = async () => {
  try {
    // First, try geolocation-based detection
    const geoLanguage = await detectUserLanguage();
    if (geoLanguage !== DEFAULT_LANGUAGE) {
      return geoLanguage;
    }

    // Fallback to browser language
    const browserLanguage = getBrowserLanguage();
    if (browserLanguage !== DEFAULT_LANGUAGE) {
      return browserLanguage;
    }

    // Final fallback
    return DEFAULT_LANGUAGE;
  } catch (error) {
    console.error('Error in comprehensive language detection:', error);
    return DEFAULT_LANGUAGE;
  }
};

/**
 * Clear cached geolocation data (useful for testing or manual refresh)
 */
export const clearGeolocationCache = () => {
  cachedCountryCode = null;
  cacheTimestamp = null;
  console.log('Geolocation cache cleared');
};

export default {
  getUserCountryCode,
  getLanguageFromCountry,
  detectUserLanguage,
  getBrowserLanguage,
  detectLanguageWithFallbacks,
  clearGeolocationCache
};
