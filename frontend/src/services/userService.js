import axios from 'axios';

import { API_URL } from '../config/env.js';

// Get user profile data
export const getUserProfile = async (userId) => {
  try {
    const response = await axios.get(`${API_URL}/users/${userId}/profile`);
    return response.data;
  } catch (error) {
    console.error('Error fetching user profile:', error);
    throw error.response?.data?.message || 'Failed to load user profile';
  }
};

// Get user's reviews
export const getUserReviews = async (userId) => {
  try {
    const response = await axios.get(`${API_URL}/users/${userId}/reviews`);
    return response.data;
  } catch (error) {
    console.error('Error fetching user reviews:', error);
    throw error.response?.data?.message || 'Failed to load user reviews';
  }
};

// Get user's community posts
export const getUserPosts = async (userId) => {
  try {
    const response = await axios.get(`${API_URL}/users/${userId}/posts`);
    return response.data;
  } catch (error) {
    console.error('Error fetching user posts:', error);
    throw error.response?.data?.message || 'Failed to load user posts';
  }
};

// For development/testing: Mock functions that return dummy data
// These can be used until the backend API is fully implemented
export const getMockUserProfile = (userId) => {
  return {
    id: userId,
    username: `user${userId}`,
    displayName: `User ${userId}`,
    title: "Game Enthusiast",
    bio: "I love indie games and sharing my thoughts about them with the community!",
    avatar: null, // Will use placeholder from the component
    memberSince: "January 2023"
  };
};

export const getMockUserReviews = (userId) => {
  return [
    {
      id: 101,
      gameId: 1,
      gameTitle: "Super Indie Adventure",
      gameImage: null, // Will use placeholder
      title: "Excellent game!",
      content: "This game has amazing graphics and gameplay. I highly recommend it to everyone.",
      rating: 5,
      date: "March 15, 2023",
      likesCount: 24,
      dislikesCount: 2,
      comments: 5
    },
    {
      id: 102,
      gameId: 2,
      gameTitle: "Pixel Dungeon Crawler",
      gameImage: null, // Will use placeholder
      title: "Good but could be better",
      content: "The game has a great art style and interesting mechanics, but it could use more content.",
      rating: 4,
      date: "April 3, 2023",
      likesCount: 12,
      dislikesCount: 3,
      comments: 2
    }
  ];
};

export const getMockUserPosts = (userId) => {
  return [
    {
      id: 201,
      title: "My favorite indie games of 2023",
      content: "Here's my list of the best indie games released so far this year...",
      excerpt: "Here's my list of the best indie games released so far this year. I've played dozens of new releases and these stood out the most.",
      date: "May 20, 2023",
      likesCount: 45,
      commentCount: 13
    },
    {
      id: 202,
      title: "Tips for new indie game developers",
      content: "If you're just starting out in game development, here are some useful tips...",
      excerpt: "If you're just starting out in game development, here are some useful tips that I've learned from my own journey.",
      date: "June 5, 2023",
      likesCount: 32,
      commentCount: 8
    }
  ];
};

// Replace the real functions with mock functions for development/testing
// Comment these out when the real API is ready
/* 
const originalGetUserProfile = getUserProfile;
const originalGetUserReviews = getUserReviews;
const originalGetUserPosts = getUserPosts;

export const getUserProfile = getMockUserProfile;
export const getUserReviews = getMockUserReviews;
export const getUserPosts = getMockUserPosts;
*/
