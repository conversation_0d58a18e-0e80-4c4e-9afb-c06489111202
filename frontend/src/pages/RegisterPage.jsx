import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const RegisterPage = () => {
  const { user, register, loading } = useAuth();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeTerms: false
  });
  const [formError, setFormError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (user) {
      navigate('/');
    }
  }, [user, navigate]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setFormError('');
    setIsLoading(true);
    
    // Validation
    if (!formData.username || !formData.email || !formData.password || !formData.confirmPassword) {
      setFormError('Please fill in all fields');
      setIsLoading(false);
      return;
    }
    
    if (formData.password !== formData.confirmPassword) {
      setFormError('Passwords do not match');
      setIsLoading(false);
      return;
    }
    
    if (!formData.agreeTerms) {
      setFormError('You must agree to the Terms and Conditions');
      setIsLoading(false);
      return;
    }

    try {
      const response = await register({
        username: formData.username,
        email: formData.email,
        password: formData.password
      });


      navigate('/'); // Redirect to home page after successful registration
    } catch (error) {
      setFormError(error.message || 'Registration failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-900">
        <div className="text-white text-lg">Loading...</div>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center w-full min-h-screen p-8 bg-gray-900 overflow-x-hidden">
      <div className="flex flex-col w-full max-w-lg p-8 bg-gray-800 rounded-xl shadow-2xl">
        <div className="w-full">
          <h1 className="text-3xl font-bold mb-2 text-white text-center">Create Account</h1>
          <p className="text-gray-300 mb-6 text-center">Join our community of indie game enthusiasts</p>
          
          {formError && (
            <div className="bg-red-500/10 text-red-400 border border-red-500/30 px-4 py-3 rounded-lg mb-4 text-sm">
              {formError}
            </div>
          )}
          
          <form className="space-y-5" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="username" className="block mb-2 text-sm text-gray-300">Username</label>
              <input
                type="text"
                id="username"
                name="username"
                placeholder="Choose a username"
                value={formData.username}
                onChange={handleChange}
                className="w-full px-4 py-3 bg-white border border-gray-600 rounded-lg text-black text-base transition-all duration-300 focus:outline-none focus:border-red-500 focus:ring-2 focus:ring-red-500/20"
              />
            </div>
            
            <div>
              <label htmlFor="email" className="block mb-2 text-sm text-gray-300">Email</label>
              <input
                type="email"
                id="email"
                name="email"
                placeholder="Enter your email"
                value={formData.email}
                onChange={handleChange}
                className="w-full px-4 py-3 bg-white border border-gray-600 rounded-lg text-black text-base transition-all duration-300 focus:outline-none focus:border-red-500 focus:ring-2 focus:ring-red-500/20"
              />
            </div>
            
            <div>
              <label htmlFor="password" className="block mb-2 text-sm text-gray-300">Password</label>
              <input
                type="password"
                id="password"
                name="password"
                placeholder="Create a password"
                value={formData.password}
                onChange={handleChange}
                className="w-full px-4 py-3 bg-white border border-gray-600 rounded-lg text-black text-base transition-all duration-300 focus:outline-none focus:border-red-500 focus:ring-2 focus:ring-red-500/20"
              />
            </div>
            
            <div>
              <label htmlFor="confirmPassword" className="block mb-2 text-sm text-gray-300">Confirm Password</label>
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                placeholder="Confirm your password"
                value={formData.confirmPassword}
                onChange={handleChange}
                className="w-full px-4 py-3 bg-white border border-gray-600 rounded-lg text-black text-base transition-all duration-300 focus:outline-none focus:border-red-500 focus:ring-2 focus:ring-red-500/20"
              />
            </div>
            
            <div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="agreeTerms"
                  name="agreeTerms"
                  checked={formData.agreeTerms}
                  onChange={handleChange}
                  className="mr-2 accent-red-500"
                />
                <label htmlFor="agreeTerms" className="text-sm text-gray-300">
                  I agree to the <a href="#terms" className="text-red-500 hover:text-orange-500 transition-colors duration-300 no-underline">Terms and Conditions</a> and <a href="#privacy" className="text-red-500 hover:text-orange-500 transition-colors duration-300 no-underline">Privacy Policy</a>
                </label>
              </div>
            </div>
            
            <button 
              type="submit" 
              className="w-full px-4 py-3 bg-gradient-to-r from-red-500 to-orange-500 text-white border-none rounded-full text-base font-bold cursor-pointer transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-red-500/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              disabled={isLoading}
            >
              {isLoading ? 'Creating Account...' : 'Create Account'}
            </button>
          </form>
          
          <div className="mt-6 text-center text-gray-400 text-sm">
            Already have an account? <Link to="/login" className="text-red-500 hover:text-orange-500 transition-colors duration-300 no-underline">Log in</Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;