import { Link } from 'react-router-dom';
import { FaExclamation<PERSON>riangle, FaHome, FaSearch, FaGamepad } from 'react-icons/fa';

const NotFoundPage = () => {
  return (
    <div className="min-h-[calc(100vh-200px)] flex justify-center items-center p-10 px-5 bg-gray-900">
      <div className="max-w-4xl w-full text-center bg-gray-800 p-12 px-8 rounded-3xl shadow-2xl relative overflow-hidden text-white">
        <div className="text-6xl mb-5 text-red-400 animate-pulse">
          <FaExclamationTriangle />
        </div>
        <h1 className="text-8xl md:text-9xl font-extrabold m-0 bg-gradient-to-r from-red-400 to-purple-500 bg-clip-text text-transparent leading-none">
          404
        </h1>
        <h2 className="text-3xl md:text-4xl m-0 mb-5 text-gray-100">
          Page Not Found
        </h2>
        <p className="text-lg md:text-xl text-gray-300 mb-10 leading-relaxed">
          Oops! The page you&apos;re looking for seems to have disappeared into the digital void.
        </p>
        
        <div className="flex justify-center gap-4 mb-12 flex-wrap">
          <Link 
            to="/" 
            className="px-5 py-3 rounded-full font-semibold text-white bg-gradient-to-r from-red-400 to-red-500 shadow-lg shadow-red-400/30 flex items-center gap-2 transition-all duration-300 hover:-translate-y-1 hover:shadow-xl hover:shadow-red-400/40 no-underline"
          >
            <FaHome /> Back to Home
          </Link>
          <Link 
            to="/search" 
            className="px-5 py-3 rounded-full font-semibold text-white bg-gradient-to-r from-purple-500 to-purple-600 shadow-lg shadow-purple-500/30 flex items-center gap-2 transition-all duration-300 hover:-translate-y-1 hover:shadow-xl hover:shadow-purple-500/40 no-underline"
          >
            <FaSearch /> Search Games
          </Link>
          <Link 
            to="/upload-game" 
            className="px-5 py-3 rounded-full font-semibold text-gray-300 bg-gray-700 border-2 border-gray-600 flex items-center gap-2 transition-all duration-300 hover:bg-gray-600 hover:-translate-y-1 no-underline"
          >
            <FaGamepad /> Upload Your Game
          </Link>
        </div>

        <div className="h-24 relative mt-8">
          <div className="h-5 bg-purple-500 absolute bottom-0 left-0 right-0 rounded"></div>
          <div 
            className="w-8 absolute bottom-5 left-1/2 transform -translate-x-1/2"
            style={{
              animation: 'walkAnimation 8s infinite linear'
            }}
          >
            <div className="w-5 h-5 bg-red-400 rounded-lg relative left-1"></div>
            <div className="w-8 h-6 bg-purple-500 mt-1 rounded"></div>
            <div className="flex justify-between px-1">
              <div 
                className="w-2 h-4 bg-gray-300 rounded"
                style={{
                  animation: 'legAnimation 0.6s infinite alternate'
                }}
              ></div>
              <div 
                className="w-2 h-4 bg-gray-300 rounded"
                style={{
                  animation: 'legAnimation 0.6s infinite alternate',
                  animationDelay: '0.3s'
                }}
              ></div>
            </div>
          </div>
        </div>
      </div>
      
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes walkAnimation {
            0% { left: 10%; }
            45% { left: 90%; }
            50% { left: 90%; transform: translateX(-50%) scaleX(-1); }
            95% { left: 10%; transform: translateX(-50%) scaleX(-1); }
            100% { left: 10%; transform: translateX(-50%) scaleX(1); }
          }
          
          @keyframes legAnimation {
            0% { transform: rotate(-10deg); }
            100% { transform: rotate(10deg); }
          }
        `
      }} />
    </div>
  );
};

export default NotFoundPage;
