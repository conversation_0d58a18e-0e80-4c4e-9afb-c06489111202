import { useEffect, useState, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { handleDiscordCallback } from '../services/discordAuthService';

const DiscordCallback = () => {
  const { login, user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const processedRef = useRef(false); // Ref to track if we already processed the auth

  useEffect(() => {
    // Immediately exit if we already have a user or we've already processed
    if (user || processedRef.current) {
      navigate('/');
      return;
    }
    
    const processDiscordLogin = async () => {
      try {
        // Exit early if we've already processed this code
        if (processedRef.current) return;
        
        // Mark as processed immediately to prevent double processing
        processedRef.current = true;
        
        setIsLoading(true);
        // Extract the code and state from URL query parameters
        const searchParams = new URLSearchParams(location.search);
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const savedState = sessionStorage.getItem('discordOAuthState');
        
        // Clean up the used state
        sessionStorage.removeItem('discordOAuthState');
        
        // Verify state if it exists (helps prevent CSRF attacks)
        if (state && savedState && state !== savedState) {
          throw new Error('Security validation failed. Please try again.');
        }

        if (!code) {
          throw new Error('No authorization code received from Discord');
        }
        
  
        
        // Add a delay to prevent race conditions with multiple requests
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Exchange code for token and user info
        const result = await handleDiscordCallback(code);
        
        // Log in the user
        login(result.user, true);
        
        // Redirect after successful login
        navigate('/');
      } catch (error) {
        console.error('Error processing Discord login:', error);
        setError(typeof error === 'string' ? error : error.message || 'Failed to authenticate with Discord');
        processedRef.current = false; // Reset the ref if we had an error
      } finally {
        setIsLoading(false);
      }
    };

    processDiscordLogin();
    
    // Cleanup function to navigate away if unmounted during processing
    return () => {
      if (processedRef.current && isLoading) {
        navigate('/');
      }
    };
  }, [location.search]); // Only depend on location.search to prevent re-runs

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex justify-center items-center">
        <div className="text-center p-8 bg-gray-800 rounded-xl shadow-2xl">
          <div className="mb-6">
            <div className="w-12 h-12 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
          </div>
          <div className="flex items-center gap-3 text-white">
            <svg className="w-6 h-6 text-indigo-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
            </svg>
            <span className="text-lg">Authenticating with Discord...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 flex justify-center items-center">
        <div className="max-w-md w-full p-8 bg-gray-800 rounded-xl shadow-2xl text-center">
          <div className="mb-6">
            <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-red-400 mb-4">Authentication Failed</h2>
            <p className="text-gray-300 mb-6 leading-relaxed">{error}</p>
            <button 
              onClick={() => navigate('/login')} 
              className="w-full px-6 py-3 bg-gradient-to-r from-red-500 to-orange-500 text-white rounded-lg font-semibold transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-red-500/30"
            >
              Back to Login
            </button>
          </div>
        </div>
      </div>
    );
  }

  return null; // We shouldn't reach here, but just in case
};

export default DiscordCallback;

// Add keyframe animation for spinner
const styleSheet = document.createElement("style");
styleSheet.textContent = `
@keyframes spin {
  to { transform: rotate(360deg); }
}
`;
document.head.appendChild(styleSheet);
