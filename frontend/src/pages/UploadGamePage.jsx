import { useState, useRef, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  FaCloudUploadAlt, FaImage, FaFileArchive, FaInfoCircle, FaLock,
  FaGamepad, FaEye } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import GamePreviewPanel from '../components/GamePreviewPanel';

import { uploadGame } from '../services/uploadService';
import { UPLOAD_CONFIG, formatFileSize, validateFileSize, validateGameFileFormat } from '../config/uploadConfig';
import { validateZipFile } from '../utils/zipValidator';
import { getCategoryOptions } from '../utils/categoryUtils';

const UploadGamePage = () => {
  const { user } = useAuth();
  const location = useLocation();
  const cardImageInputRef = useRef(null);
  const gifAnimationInputRef = useRef(null);

  // Preview panel state
  const [previewOpen, setPreviewOpen] = useState(false);

  // Upload progress state
  const [uploadProgress, setUploadProgress] = useState(0);

  // Form state
  const [gameData, setGameData] = useState({
    title: '',
    description: '',
    genre: '',
    tags: '',

    // Web game fields (enforced as true for web games only)
    isWebGame: true,
    webGameUrl: '',
    webGameType: 'webgl', // Default to WebGL
    hasEmbeddedVersion: true, // Always true for uploaded web games

    // Release date
    releaseDate: new Date().toISOString().split('T')[0] // YYYY-MM-DD format
  });

  // File upload states
  const [gameFile, setGameFile] = useState(null);
  const [cardImage, setCardImage] = useState(null);
  const [gifAnimation, setGifAnimation] = useState(null);
  const [cardImagePreview, setCardImagePreview] = useState(null);
  const [gifAnimationPreview, setGifAnimationPreview] = useState(null);

  // ZIP validation states
  const [zipValidation, setZipValidation] = useState(null);
  const [isValidatingZip, setIsValidatingZip] = useState(false);

  // Validation and UI states
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [validationFailed, setValidationFailed] = useState(false);
  const errorSummaryRef = useRef(null);

  // Add useEffect to check if game has purchases (when editing an existing game)
  useEffect(() => {
    // This would normally check the game data from the server
    // For now we'll just simulate with a placeholder
    if (location.pathname.includes('/edit/') && gameData.id) {
      // You would make an API call here to check if the game has purchases
      checkGamePurchases(gameData.id);
    }
  }, [gameData.id, location.pathname]);
  
  // Function to check if a game has any purchases
  const checkGamePurchases = async () => {
    try {
      // This would be an API call in a real implementation
      // const result = await api.checkGamePurchases(gameId);
      // setGameHasPurchases(result.hasPurchases);
      
      // For demonstration, we'll just set it to false
      // setGameHasPurchases(false);
    } catch (error) {
      console.error('Error checking game purchases:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    
    // Special handling for tags to limit to 3
    if (name === 'tags') {
      const tags = value.split(',').map(tag => tag.trim()).filter(tag => tag);
      
      if (tags.length > 3) {
        // If more than 3 tags, show error and limit to first 3
        setErrors({
          ...errors,
          tags: "Maximum 3 tags allowed"
        });
        // Only keep the first 3 tags
        const limitedTags = tags.slice(0, 3).join(', ');
        setGameData({
          ...gameData,
          tags: limitedTags
        });
        return;
      }
    }
    
    setGameData({
      ...gameData,
      [name]: value
    });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };



  // Enhanced function to handle single game file upload with ZIP validation
  const handleGameFileChange = async (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Basic validation first
      if (!validateGameFileFormat(file)) {
        setErrors({
          ...errors,
          gameFile: "Game file must be in ZIP format"
        });
        return;
      }

      if (!validateFileSize(file)) {
        setErrors({
          ...errors,
          gameFile: `Game file size must not exceed ${formatFileSize(UPLOAD_CONFIG.MAX_FILE_SIZE)}`
        });
        return;
      }

      setGameFile(file);
      setIsValidatingZip(true);

      // Clear previous errors
      setErrors({
        ...errors,
        gameFile: null
      });

      try {
        // Validate ZIP contents
        const validation = await validateZipFile(file);
        setZipValidation(validation);

        if (!validation.isValid) {
          setErrors({
            ...errors,
            gameFile: validation.errors[0] || "Invalid ZIP file structure"
          });
        }
      } catch (error) {
        console.error('ZIP validation error:', error);
        setErrors({
          ...errors,
          gameFile: "Failed to validate ZIP file. Please ensure it's a valid ZIP archive."
        });
      } finally {
        setIsValidatingZip(false);
      }
    }
  };



  const handleCardImageChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setCardImage(file);

      // Preview
      const reader = new FileReader();
      reader.onload = (e) => setCardImagePreview(e.target.result);
      reader.readAsDataURL(file);

      setErrors({
        ...errors,
        cardImage: null
      });
    }
  };

  // Handle GIF animation upload
  const handleGifAnimationChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setGifAnimation(file);

      // Preview
      const reader = new FileReader();
      reader.onload = (e) => setGifAnimationPreview(e.target.result);
      reader.readAsDataURL(file);
    }
  };





  const validateForm = () => {
    const newErrors = {};

    // Title validation
    if (!gameData.title.trim()) {
      newErrors.title = "Game title is required";
    }

    // Description validation
    if (!gameData.description.trim()) {
      newErrors.description = "Game description is required";
    } else if (gameData.description.trim().length < UPLOAD_CONFIG.MIN_DESCRIPTION_LENGTH) {
      newErrors.description = `Description must be at least ${UPLOAD_CONFIG.MIN_DESCRIPTION_LENGTH} characters long`;
    }

    // Genre validation
    if (!gameData.genre) {
      newErrors.genre = "Please select a genre";
    }

    // Tags validation - check if at least 1 tag exists
    const tags = gameData.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    if (tags.length === 0) {
      newErrors.tags = "Please enter at least 1 tag";
    }

    // Game file validation - require a single ZIP file
    if (!gameFile) {
      newErrors.gameFile = "Game file is required (must be a ZIP file containing your web game)";
    } else {
      // Validate file format
      if (!validateGameFileFormat(gameFile)) {
        newErrors.gameFile = "Game file must be in ZIP format";
      }

      // Validate file size
      if (!validateFileSize(gameFile)) {
        newErrors.gameFile = `Game file size must not exceed ${formatFileSize(UPLOAD_CONFIG.MAX_FILE_SIZE)}`;
      }
    }

    // Card image validation
    if (!cardImage) {
      newErrors.cardImage = "Card image is required";
    }

    // Make GIF animation required
    if (!gifAnimation) {
      newErrors.gifAnimation = "Hover animation GIF is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };



  const handleSubmit = async (e) => {
    e.preventDefault();

    // Scroll to top if there were previous errors
    if (validationFailed) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    if (!validateForm()) {
      setValidationFailed(true); // Set validation failed flag
      
      // Scroll to error summary
      setTimeout(() => {
        if (errorSummaryRef.current) {
          errorSummaryRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
      
      return;
    }

    setValidationFailed(false); // Reset validation failed flag
    setIsSubmitting(true);
    setUploadProgress(0); // Reset progress

    try {
      // Call the uploadGame service function with progress tracking
      await uploadGame(
        gameData,
        gameFile, // Now passing single game file
        null, // coverImage removed
        cardImage,
        gifAnimation,
        [], // screenshots removed
        (progress) => setUploadProgress(progress) // Progress callback
      );
      
      
      setSubmitSuccess(true);
      
      // Reset form
      setGameData({
        title: '',
        description: '',
        genre: '',
        tags: '',

        // Web game fields (enforced as true for web games only)
        isWebGame: true,
        webGameUrl: '',
        webGameType: 'webgl', // Default to WebGL
        hasEmbeddedVersion: true, // Always true for uploaded web games

        // Release date
        releaseDate: new Date().toISOString().split('T')[0] // YYYY-MM-DD format
      });
      setGameFile(null); // Reset game file
      setCardImage(null);
      setGifAnimation(null);
      setCardImagePreview(null);
      setGifAnimationPreview(null);
      setZipValidation(null); // Reset ZIP validation
      setUploadProgress(0); // Reset upload progress

      // Reset file input references
      if (cardImageInputRef.current) cardImageInputRef.current.value = '';
      if (gifAnimationInputRef.current) gifAnimationInputRef.current.value = '';
      
      // Navigate to the game page after successful upload
      // Uncomment this when you're ready to implement it
      // navigate(`/game/${result.gameId}`);
      
    } catch (error) {
      console.error('Upload error:', error);
      
      setErrors({
        ...errors,
        submit: error.message || "An error occurred while uploading your game. Please try again."
      });
      
      // Scroll to the error
      window.scrollTo({ top: 0, behavior: 'smooth' });
      
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to count errors
  const countErrors = () => {
    return Object.keys(errors).length;
  };

  // Helper function to generate error message list
  const generateErrorList = () => {
    return Object.entries(errors).map(([field, message]) => {
      // Map field names to more readable forms
      const readableFieldNames = {
        title: "Game Title",
        description: "Game Description",
        genre: "Genre",
        tags: "Tags",
        gameFile: "Game File",
        cardImage: "Card Thumbnail Image",
        gifAnimation: "Hover Animation GIF"
      };

      const fieldName = readableFieldNames[field] || field;
      return { field, message, fieldName };
    });
  };

  // Toggle preview panel
  const togglePreview = () => {
    setPreviewOpen(!previewOpen);
  };

  // Close preview panel
  const closePreview = () => {
    setPreviewOpen(false);
  };
  


  // Render different content based on authentication status
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <FaGamepad className="text-orange-500 text-8xl mx-auto mb-8" />
            <h1 className="text-5xl font-bold text-white mb-4">Share Your Game with the World!</h1>
            <p className="text-xl text-gray-300 mb-12">Upload and distribute your indie games for FREE on IndieRepo</p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <FaCloudUploadAlt className="text-blue-500 text-4xl mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Free Uploads</h3>
                <p className="text-gray-300">Distribute your games without paying any platform fees</p>
              </div>

              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <FaLock className="text-green-500 text-4xl mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Monetization Options</h3>
                <p className="text-gray-300">Choose between free games, fixed pricing or our credit system</p>
              </div>

              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <FaInfoCircle className="text-purple-500 text-4xl mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Community Exposure</h3>
                <p className="text-gray-300">Get your game in front of passionate indie game players</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Link to="/login" className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-200">
                Login to Upload Your Game
              </Link>
              <Link to="/register" className="bg-gray-700 hover:bg-gray-600 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors duration-200">
                Create an Account
              </Link>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 max-w-2xl mx-auto">
              <blockquote className="text-lg text-gray-300 italic">
                &ldquo;IndieRepo helped me share my passion project with players worldwide without any upfront costs!&rdquo;
                <footer className="text-orange-400 font-medium mt-2">- Indie Developer</footer>
              </blockquote>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // For logged-in users, show the upload form
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <h1 className="flex items-center gap-3 text-4xl font-bold text-white mb-8">
          <FaCloudUploadAlt className="text-orange-500" /> Upload Your Game
        </h1>

        <button 
          className="fixed top-4 right-4 z-40 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-lg shadow-lg transition-colors duration-200 flex items-center gap-2"
          onClick={togglePreview}
          title="Preview Game Page"
        >
          <FaEye /> {window.innerWidth > 992 ? "Preview Game Page" : ""}
        </button>



        {submitSuccess ? (
          <div className="bg-green-900 bg-opacity-50 border border-green-600 rounded-lg p-8 text-center">
            <h2 className="text-2xl font-bold text-green-200 mb-4">Game Uploaded Successfully!</h2>
            <p className="text-green-300 mb-6">Your game has been submitted and will be available shortly after review.</p>
            <button 
              className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200" 
              onClick={() => setSubmitSuccess(false)}
            >
              Upload Another Game
            </button>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Error summary - only visible when validation fails */}
            {validationFailed && (
              <div className="bg-red-900 bg-opacity-50 border border-red-600 rounded-lg p-6" ref={errorSummaryRef}>
                <h3 className="flex items-center gap-2 text-red-200 text-lg font-semibold mb-3">
                  <FaInfoCircle className="text-red-400" /> Please complete all required fields
                </h3>
                <p className="text-red-300 mb-4">The following {countErrors()} {countErrors() === 1 ? 'field' : 'fields'} need attention:</p>
                <ul className="list-disc list-inside space-y-1 text-red-300 mb-4">
                  {generateErrorList().map((error, index) => (
                    <li key={index}>{error.fieldName} - {error.message}</li>
                  ))}
                </ul>
                <p className="text-red-400 text-sm">Required fields are marked with an asterisk (*)</p>
              </div>
            )}

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h2 className="text-2xl font-bold text-white mb-4">Game Card Images</h2>
              <p className="text-gray-300 mb-6">
                These images will be used as the thumbnail in game listings. Choose attention-grabbing images that represent your game well.
              </p>

              <div className="space-y-6">
                <div>
                  <label htmlFor="cardImage" className="block text-white font-medium mb-2">Card Thumbnail Image *</label>
                  <div className="space-y-2">
                    <input
                      type="file"
                      id="cardImage"
                      ref={cardImageInputRef}
                      onChange={handleCardImageChange}
                      accept=".png,.jpg,.jpeg"
                      className="hidden"
                    />
                    <div
                      className={`border-2 border-dashed rounded-lg p-6 cursor-pointer transition-colors duration-200 ${
                        errors.cardImage ? 'border-red-500 bg-red-900 bg-opacity-20' :
                        cardImage ? 'border-green-500 bg-green-900 bg-opacity-20' :
                        'border-gray-600 bg-gray-700 hover:border-orange-500 hover:bg-gray-600'
                      }`}
                      onClick={() => cardImageInputRef.current.click()}
                    >
                      {cardImagePreview ? (
                        <img src={cardImagePreview} alt="Card preview" className="w-full h-32 object-cover rounded-lg" />
                      ) : (
                        <div className="text-center">
                          <FaImage className="text-gray-400 text-4xl mx-auto mb-2" />
                          <span className="text-gray-300">Click to select a card image</span>
                        </div>
                      )}
                    </div>
                  </div>
                  {errors.cardImage && <div className="text-red-400 text-sm">{errors.cardImage}</div>}
                  <div className="text-gray-500 text-sm">Recommended size: 280x158 px (16:9 ratio) - PNG or JPG only</div>
                </div>

                <div>
                  <label htmlFor="gifAnimation" className="block text-white font-medium mb-2">Hover Animation (GIF) *</label>
                  <div className="space-y-2">
                    <input
                      type="file"
                      id="gifAnimation"
                      ref={gifAnimationInputRef}
                      onChange={handleGifAnimationChange}
                      accept=".gif"
                      className="hidden"
                    />
                    <div
                      className={`border-2 border-dashed rounded-lg p-6 cursor-pointer transition-colors duration-200 ${
                        errors.gifAnimation ? 'border-red-500 bg-red-900 bg-opacity-20' :
                        gifAnimation ? 'border-green-500 bg-green-900 bg-opacity-20' :
                        'border-gray-600 bg-gray-700 hover:border-orange-500 hover:bg-gray-600'
                      }`}
                      onClick={() => gifAnimationInputRef.current.click()}
                    >
                      {gifAnimationPreview ? (
                        <img src={gifAnimationPreview} alt="GIF preview" className="w-full h-32 object-cover rounded-lg" />
                      ) : (
                        <div className="text-center">
                          <FaImage className="text-gray-400 text-4xl mx-auto mb-2" />
                          <span className="text-gray-300">Click to select a GIF for hover animation</span>
                        </div>
                      )}
                    </div>
                  </div>
                  {errors.gifAnimation && <div className="text-red-400 text-sm">{errors.gifAnimation}</div>}
                  <div className="text-gray-500 text-sm">Recommended size: 280x270px - Will replace card on hover</div>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h2 className="text-2xl font-bold text-white mb-6">Game Information</h2>

              <div className="space-y-6">
                <div>
                  <label htmlFor="title" className="block text-white font-medium mb-2">Game Title *</label>
                  <input 
                    type="text" 
                    id="title" 
                    name="title" 
                    value={gameData.title}
                    onChange={handleInputChange}
                    className={`w-full bg-gray-700 border rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                      errors.title ? 'border-red-500' : 'border-gray-600'
                    }`}
                    placeholder="Enter your game title"
                  />
                  {errors.title && <div className="text-red-400 text-sm mt-1">{errors.title}</div>}
                </div>

                <div>
                  <label htmlFor="description" className="block text-white font-medium mb-2">Game Description *</label>
                  <textarea 
                    id="description" 
                    name="description" 
                    value={gameData.description}
                    onChange={handleInputChange}
                    rows="6"
                    className={`w-full bg-gray-700 border rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-vertical ${
                      errors.description ? 'border-red-500' : 'border-gray-600'
                    }`}
                    placeholder="Describe your game..."
                  ></textarea>
                  {errors.description && <div className="text-red-400 text-sm mt-1">{errors.description}</div>}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="genre" className="block text-white font-medium mb-2">Genre *</label>
                    <select
                      id="genre"
                      name="genre"
                      value={gameData.genre}
                      onChange={handleInputChange}
                      className={`w-full bg-gray-700 border rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                        errors.genre ? 'border-red-500' : 'border-gray-600'
                      }`}
                    >
                      <option value="">Select a genre</option>
                      {getCategoryOptions().map((category) => (
                        <option key={category.value} value={category.value}>
                          {category.label}
                        </option>
                      ))}
                    </select>
                    {errors.genre && <div className="text-red-400 text-sm mt-1">{errors.genre}</div>}
                  </div>

                  <div>
                    <label htmlFor="tags" className="block text-white font-medium mb-2">Tags (max 3, comma separated) *</label>
                    <input 
                      type="text" 
                      id="tags" 
                      name="tags" 
                      value={gameData.tags}
                      onChange={handleInputChange}
                      placeholder="e.g. 2D, Pixel Art, Roguelike"
                      className={`w-full bg-gray-700 border rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                        errors.tags ? 'border-red-500' : 'border-gray-600'
                      }`}
                    />
                    {errors.tags && <div className="text-red-400 text-sm mt-1">{errors.tags}</div>}
                    <div className="text-gray-500 text-sm mt-1">Enter 1-3 tags that describe your game</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h2 className="text-2xl font-bold text-white mb-4">Web Game File *</h2>
              <p className="text-gray-300 mb-4">
                Upload your web game as a ZIP file. The ZIP must contain an index.html file in the root folder.
              </p>
              <div className="bg-blue-900 bg-opacity-50 border border-blue-600 rounded-lg p-4 mb-6">
                <div className="flex items-start gap-3">
                  <FaInfoCircle className="text-blue-400 text-lg mt-0.5" />
                  <div>
                    <p className="text-blue-200 font-medium mb-2">Web Game Requirements:</p>
                    <ul className="text-blue-200 text-sm space-y-1">
                      <li>• Must be a ZIP file (WebGL/HTML5 games only)</li>
                      <li>• Must contain index.html in the root folder</li>
                      <li>• Should include .gzip or .br compressed files for optimal loading</li>
                      <li>• Maximum file size: {formatFileSize(UPLOAD_CONFIG.MAX_FILE_SIZE)}</li>
                    </ul>
                  </div>
                </div>
              </div>

              {errors.gameFile && <div className="bg-red-900 bg-opacity-50 border border-red-600 text-red-200 p-3 rounded-lg mb-4">{errors.gameFile}</div>}

              <div className="space-y-6">
                <input
                  type="file"
                  id="gameFile"
                  onChange={handleGameFileChange}
                  accept=".zip"
                  className="hidden"
                />
                <div
                  className={`border-2 border-dashed rounded-lg p-6 cursor-pointer transition-colors duration-200 ${
                    errors.gameFile ? 'border-red-500 bg-red-900 bg-opacity-20' :
                    gameFile && zipValidation?.isValid
                      ? 'border-green-500 bg-green-900 bg-opacity-20'
                      : gameFile && zipValidation && !zipValidation.isValid
                      ? 'border-yellow-500 bg-yellow-900 bg-opacity-20'
                      : 'border-gray-500 bg-gray-600 hover:border-orange-500 hover:bg-gray-500'
                  }`}
                  onClick={() => document.getElementById('gameFile').click()}
                >
                  <div className="text-center">
                    <FaFileArchive className="text-gray-300 text-4xl mx-auto mb-2" />
                    <span className="text-gray-200">
                      {isValidatingZip ? 'Validating ZIP file...' :
                       gameFile ? gameFile.name : 'Click to select your game file'}
                    </span>
                    {gameFile && (
                      <div className="text-sm text-gray-400 mt-1">
                        Size: {formatFileSize(gameFile.size)}
                      </div>
                    )}
                  </div>
                </div>

                <div className="text-gray-400 text-sm">
                  Maximum file size: {formatFileSize(UPLOAD_CONFIG.MAX_FILE_SIZE)}. Only ZIP format allowed for web games.
                </div>

                {/* ZIP Validation Results */}
                {zipValidation && gameFile && (
                  <div className={`mt-4 p-4 rounded-lg border ${
                    zipValidation.isValid
                      ? 'bg-green-900 bg-opacity-50 border-green-600'
                      : 'bg-yellow-900 bg-opacity-50 border-yellow-600'
                  }`}>
                    <h4 className={`font-medium mb-2 ${
                      zipValidation.isValid ? 'text-green-200' : 'text-yellow-200'
                    }`}>
                      ZIP Validation Results:
                    </h4>
                    <div className="space-y-1 text-sm">
                      <div className={`flex items-center gap-2 ${
                        zipValidation.hasIndexHtml ? 'text-green-300' : 'text-red-300'
                      }`}>
                        <span>{zipValidation.hasIndexHtml ? '✓' : '✗'}</span>
                        <span>index.html in root folder</span>
                      </div>
                      <div className={`flex items-center gap-2 ${
                        zipValidation.hasCompressedFiles ? 'text-green-300' : 'text-yellow-300'
                      }`}>
                        <span>{zipValidation.hasCompressedFiles ? '✓' : '⚠'}</span>
                        <span>Compressed files (.gzip/.br) {zipValidation.hasCompressedFiles ? 'found' : 'recommended'}</span>
                      </div>
                    </div>
                    {zipValidation.errors.length > 0 && (
                      <div className="mt-2 space-y-1">
                        {zipValidation.errors.map((error, index) => (
                          <div key={index} className="text-yellow-300 text-xs">
                            • {error}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

              </div>
            </div>







            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h2 className="text-2xl font-bold text-white mb-4">Web Game Configuration</h2>
              <p className="text-gray-300 mb-6">All games on this platform are web games that play directly in the browser</p>

              <div className="space-y-6">
                <div className="bg-green-900 bg-opacity-50 border border-green-600 rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <input
                      type="checkbox"
                      id="isWebGame"
                      name="isWebGame"
                      checked={gameData.isWebGame}
                      disabled={true}
                      className="w-4 h-4 text-green-500 bg-gray-600 border-gray-500 rounded focus:ring-green-500 focus:ring-2"
                    />
                    <label htmlFor="isWebGame" className="text-green-200 font-semibold">
                      ✓ Web Game (playable in browser)
                    </label>
                  </div>
                  <p className="text-green-300 text-sm">
                    This platform only supports web games (WebGL/HTML5) that can be played directly in the browser
                  </p>
                </div>

                <div className="space-y-4">
                  <div>
                    <label htmlFor="webGameType" className="block text-white font-medium mb-2">Web Game Technology *</label>
                    <select
                      id="webGameType"
                      name="webGameType"
                      value={gameData.webGameType}
                      onChange={handleInputChange}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    >
                      <option value="webgl">WebGL (Unity, Unreal, etc.)</option>
                      <option value="html5">HTML5/JavaScript</option>
                      <option value="unity">Unity WebGL</option>
                    </select>
                    <div className="text-gray-400 text-sm mt-1">
                      Select the technology used to build your web game
                    </div>
                  </div>

                    <div>
                      <label htmlFor="webGameUrl" className="block text-white font-medium mb-2">External Web Game URL (Optional)</label>
                      <input 
                        type="text" 
                        id="webGameUrl" 
                        name="webGameUrl" 
                        value={gameData.webGameUrl}
                        onChange={handleInputChange}
                        placeholder="e.g., https://yourgame.com/play or leave empty if uploading files"
                        className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      />
                      <div className="text-gray-400 text-sm mt-1">
                        If your game is hosted elsewhere, provide the direct play URL. Otherwise, upload web-playable files above.
                      </div>
                    </div>

                  <div className="bg-green-900 bg-opacity-50 border border-green-600 rounded-lg p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <input
                        type="checkbox"
                        id="hasEmbeddedVersion"
                        name="hasEmbeddedVersion"
                        checked={gameData.hasEmbeddedVersion}
                        disabled={true}
                        className="w-4 h-4 text-green-500 bg-gray-600 border-gray-500 rounded focus:ring-green-500 focus:ring-2"
                      />
                      <label htmlFor="hasEmbeddedVersion" className="text-green-200 font-semibold">
                        ✓ Embedded version for direct play
                      </label>
                    </div>
                    <p className="text-green-300 text-sm">
                      Your uploaded ZIP file will be extracted and made playable directly on the game page
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h2 className="text-2xl font-bold text-white mb-4">Release Information</h2>
              
              <div>
                <label htmlFor="releaseDate" className="block text-white font-medium mb-2">Release Date</label>
                <input 
                  type="date" 
                  id="releaseDate" 
                  name="releaseDate" 
                  value={gameData.releaseDate}
                  onChange={handleInputChange}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
                <div className="text-gray-400 text-sm mt-1">
                  When was your game first released?
                </div>
              </div>
            </div>



            {errors.submit && <div className="bg-red-900 bg-opacity-50 border border-red-600 text-red-200 p-4 rounded-lg">{errors.submit}</div>}

            <div className="flex flex-col items-center pt-6 space-y-4">
              {/* Upload Progress Bar */}
              {isSubmitting && (
                <div className="w-full max-w-md">
                  <div className="flex justify-between text-sm text-gray-300 mb-2">
                    <span>Uploading your game...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-red-500 to-orange-500 h-3 rounded-full transition-all duration-300 ease-out"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-400 mt-1 text-center">
                    {uploadProgress < 100 ? 'Please wait while your game is being uploaded...' : 'Processing your game...'}
                  </div>
                </div>
              )}

              <button
                type="submit"
                className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 disabled:from-gray-600 disabled:to-gray-600 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-200 disabled:cursor-not-allowed"
                disabled={isSubmitting || (zipValidation && !zipValidation.isValid)}
              >
                {isSubmitting ? `Uploading... ${uploadProgress}%` : 'Upload Game'}
              </button>
            </div>
            
          </form>
        )}
      </div>

      {/* Game Preview Panel */}
      <GamePreviewPanel
        isOpen={previewOpen}
        onClose={closePreview}
        gameData={gameData}
        gameFiles={gameFile ? [gameFile] : []}
        coverImagePreview={null}
        cardImagePreview={cardImagePreview}
        gifAnimationPreview={gifAnimationPreview}
        screenshotPreviews={[]}
      />

      {/* Overlay for mobile view */}
      {previewOpen && window.innerWidth <= 992 && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-30" onClick={closePreview}></div>
      )}
    </div>
  );
};

export default UploadGamePage;
