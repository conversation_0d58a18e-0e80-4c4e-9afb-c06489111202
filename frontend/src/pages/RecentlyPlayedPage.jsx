import { useState, useEffect } from 'react';
import GameCard from '../components/GameCard';
import Sidebar from '../components/Sidebar';
import GameLoader from '../components/GameLoader';
import { FaClock, FaTrash } from 'react-icons/fa';
import { useLanguage } from '../context/LanguageContext';
import { useAuth } from '../context/AuthContext';
import { useSidebar } from '../context/SidebarContext';

const RecentlyPlayedPage = () => {
  const { t } = useLanguage();
  const { user } = useAuth();
  const { isSidebarOpen, toggleSidebar } = useSidebar();

  // Recently played games (stored in localStorage for now)
  const [recentlyPlayed, setRecentlyPlayed] = useState([]);
  const [loading, setLoading] = useState(true);

  // Load recently played games from localStorage
  useEffect(() => {
    const loadRecentlyPlayed = () => {
      try {
        const stored = localStorage.getItem('recentlyPlayed');
        if (stored) {
          const games = JSON.parse(stored);
          // Sort by last played time (most recent first)
          const sortedGames = games.sort((a, b) => new Date(b.lastPlayed) - new Date(a.lastPlayed));
          setRecentlyPlayed(sortedGames);
        }
      } catch (error) {
        console.error('Error loading recently played games:', error);
      } finally {
        setLoading(false);
      }
    };

    loadRecentlyPlayed();
  }, []);

  // Clear all recently played games
  const clearAllRecentlyPlayed = () => {
    localStorage.removeItem('recentlyPlayed');
    setRecentlyPlayed([]);
  };

  // Remove a specific game from recently played
  const removeFromRecentlyPlayed = (gameId) => {
    const updatedGames = recentlyPlayed.filter(game => game.id !== gameId);
    setRecentlyPlayed(updatedGames);
    localStorage.setItem('recentlyPlayed', JSON.stringify(updatedGames));
  };

  // Format last played time
  const formatLastPlayed = (timestamp) => {
    const now = new Date();
    const played = new Date(timestamp);
    const diffInMinutes = Math.floor((now - played) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} days ago`;
    
    return played.toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-b from-[#121212] to-[#0a0a0a] flex">
        <Sidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />
        <div className={`flex-1 transition-all duration-300 ${isSidebarOpen ? 'lg:pl-64' : 'lg:pl-0'} p-5 flex items-center justify-center`}>
          <GameLoader size="lg" variant="controller" />
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen bg-gradient-to-b from-[#121212] to-[#0a0a0a] flex">
      <Sidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />
      
      <div className={`flex-1 transition-all duration-300 ${isSidebarOpen ? 'lg:pl-64' : 'lg:pl-0'}`}>
        {/* Header */}
        <div className="bg-gray-800 border-b border-gray-700 p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <FaClock className="text-green-400 text-2xl" />
                <h1 className="text-3xl font-bold text-white">{t('sidebar.navigation.recentlyPlayed')}</h1>
              </div>
              
              {recentlyPlayed.length > 0 && (
                <button
                  onClick={clearAllRecentlyPlayed}
                  className="flex items-center gap-2 px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors duration-200"
                >
                  <FaTrash />
                  <span>Clear All</span>
                </button>
              )}
            </div>
            
            <p className="text-gray-400 mt-2">
              {recentlyPlayed.length > 0 
                ? `${recentlyPlayed.length} recently played games`
                : 'No recently played games'
              }
            </p>
          </div>
        </div>

        <div className="max-w-7xl mx-auto p-6">
          {!user ? (
            <div className="flex flex-col items-center justify-center py-16">
              <div className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mb-4">
                <FaClock className="text-2xl text-gray-400" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Sign in to track your games</h3>
              <p className="text-gray-400 mb-6 text-center">
                Sign in to automatically track the games you play and access them easily from this page.
              </p>
              <a
                href="/login"
                className="px-6 py-3 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors duration-200"
              >
                Sign In
              </a>
            </div>
          ) : recentlyPlayed.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-16">
              <div className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mb-4">
                <FaClock className="text-2xl text-gray-400" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">No recently played games</h3>
              <p className="text-gray-400 mb-6 text-center">
                Games you play will appear here for easy access. Start playing some games to build your history!
              </p>
              <a
                href="/"
                className="px-6 py-3 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors duration-200"
              >
                Browse Games
              </a>
            </div>
          ) : (
            <div className="space-y-6">
              {recentlyPlayed.map((game, index) => (
                <div key={`${game.id}-${index}`} className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                  <div className="flex items-center gap-4">
                    {/* Game Card */}
                    <div className="w-48 flex-shrink-0">
                      <GameCard game={game} />
                    </div>
                    
                    {/* Game Info */}
                    <div className="flex-1 min-w-0">
                      <h3 className="text-xl font-bold text-white mb-2 truncate">{game.title}</h3>
                      <p className="text-gray-400 mb-3 line-clamp-2">{game.description}</p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span>Last played: {formatLastPlayed(game.lastPlayed)}</span>
                          {game.playTime && (
                            <span>Play time: {Math.floor(game.playTime / 60)} minutes</span>
                          )}
                        </div>
                        
                        <button
                          onClick={() => removeFromRecentlyPlayed(game.id)}
                          className="p-2 text-gray-400 hover:text-red-400 transition-colors duration-200"
                          title="Remove from recently played"
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RecentlyPlayedPage;
