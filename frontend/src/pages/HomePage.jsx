import { useState, useEffect } from 'react';
import GameLoader from '../components/GameLoader';
import CategorySection from '../components/CategorySection';
import axios from 'axios';
import { API_URL } from '../config/env.js';
import { getGameCategories } from '../utils/categoryUtils';

const HomePage = () => {

  // State for category-based games data - initialize with categories from JSON
  const [categoryGames, setCategoryGames] = useState(() => {
    const initialState = {};
    getGameCategories().forEach(category => {
      initialState[category.value] = [];
    });
    return initialState;
  });

  // Add loading and error states
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Category configuration - use categories from JSON
  const categories = getGameCategories().map(category => ({
    key: category.value,
    title: `${category.label} Games`,
    slug: category.slug
  }));



  // Fetch games from the API and categorize them
  useEffect(() => {
    const fetchGames = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/games`);

        // Extract the games array from the response
        const gamesArray = response.data.games || [];

        if (!Array.isArray(gamesArray)) {
          console.error('Expected games array not found in response:', response.data);
          throw new Error('Invalid response format from API');
        }

        // Transform the database data to match the expected format for GameCard
        const formattedGames = gamesArray.map((game, index) => {
          // Determine game status based on release date and other factors
          let status = null;
          const releaseDate = new Date(game.releaseDate);
          const now = new Date();
          const daysSinceRelease = (now - releaseDate) / (1000 * 60 * 60 * 24);

          if (daysSinceRelease <= 7) {
            status = 'NEW';
          } else if (daysSinceRelease <= 30 && game.lastUpdated) {
            const lastUpdated = new Date(game.lastUpdated);
            const daysSinceUpdate = (now - lastUpdated) / (1000 * 60 * 60 * 24);
            if (daysSinceUpdate <= 7) {
              status = 'UPDATED';
            }
          }

          // Mark some games as HOT based on popularity (placeholder logic)
          if (index < 3 && !status) {
            status = 'HOT';
          }

          return {
            id: game.id,
            title: game.title,
            description: game.description,
            image: game.cardImage || game.image,
            hoverGif: game.animationGif,
            genre: game.genre,
            tags: game.tags ? game.tags.split(',').map(tag => tag.trim()) : [],
            paymentType: game.priceModel,
            price: game.priceModel === 'paid' ? `$${parseFloat(game.price).toFixed(2)}` :
                   game.priceModel === 'credits' ? `${game.creditPrice} Credits` : 'Free',
            releaseDate: releaseDate,
            status: status
          };
        });

        // Categorize games by genre/tags
        const categorizedGames = {
          driving: [],
          action: [],
          puzzle: [],
          adventure: [],
          sports: [],
          racing: [],
          strategy: [],
          arcade: []
        };

        // Categorize games based on genre and tags
        formattedGames.forEach(game => {
          const gameGenre = game.genre?.toLowerCase() || '';
          const gameTags = game.tags?.map(tag => tag.toLowerCase()) || [];

          // Driving/Car games
          if (gameGenre.includes('car') || gameGenre.includes('driving') ||
              gameTags.some(tag => ['car', 'driving', 'vehicle', 'truck'].includes(tag))) {
            categorizedGames.driving.push(game);
          }

          // Action games
          if (gameGenre.includes('action') || gameGenre.includes('shooter') ||
              gameTags.some(tag => ['action', 'shooter', 'combat', 'fighting'].includes(tag))) {
            categorizedGames.action.push(game);
          }

          // Puzzle games
          if (gameGenre.includes('puzzle') || gameGenre.includes('brain') ||
              gameTags.some(tag => ['puzzle', 'brain', 'logic', 'match'].includes(tag))) {
            categorizedGames.puzzle.push(game);
          }

          // Adventure games
          if (gameGenre.includes('adventure') || gameGenre.includes('rpg') ||
              gameTags.some(tag => ['adventure', 'rpg', 'quest', 'exploration'].includes(tag))) {
            categorizedGames.adventure.push(game);
          }

          // Sports games
          if (gameGenre.includes('sport') || gameGenre.includes('ball') ||
              gameTags.some(tag => ['sport', 'ball', 'football', 'basketball', 'soccer'].includes(tag))) {
            categorizedGames.sports.push(game);
          }

          // Racing games
          if (gameGenre.includes('racing') || gameGenre.includes('race') ||
              gameTags.some(tag => ['racing', 'race', 'speed', 'fast'].includes(tag))) {
            categorizedGames.racing.push(game);
          }

          // Strategy games
          if (gameGenre.includes('strategy') || gameGenre.includes('tower') ||
              gameTags.some(tag => ['strategy', 'tower', 'defense', 'tactical'].includes(tag))) {
            categorizedGames.strategy.push(game);
          }

          // Arcade games (fallback for others)
          if (gameGenre.includes('arcade') || gameGenre.includes('casual') ||
              gameTags.some(tag => ['arcade', 'casual', 'retro', 'classic'].includes(tag))) {
            categorizedGames.arcade.push(game);
          }
        });

        // Fill empty categories with random games
        Object.keys(categorizedGames).forEach(category => {
          if (categorizedGames[category].length === 0) {
            // Add some random games to empty categories
            const shuffled = [...formattedGames].sort(() => 0.5 - Math.random());
            categorizedGames[category] = shuffled.slice(0, 8);
          } else {
            // Limit to 12 games per category for performance
            categorizedGames[category] = categorizedGames[category].slice(0, 12);
          }
        });

        setCategoryGames(categorizedGames);

      } catch (err) {
        console.error('Error fetching games:', err);
        setError('Failed to load games. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchGames();
  }, []);



  // Render loading state
  if (loading) {
    return <GameLoader fullScreen={true} size="lg" variant="gamepad" />;
  }

  // Render error state
  if (error) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-b from-[#121212] to-[#0a0a0a] p-5 flex items-center justify-center">
        <div className="text-red-500 text-xl">{error}</div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen bg-gradient-to-b from-[#121212] to-[#0a0a0a] py-6">
      {/* Category Sections */}
      {categories.map((category) => (
        <CategorySection
          key={category.key}
          title={category.title}
          games={categoryGames[category.key]}
          categorySlug={category.slug}
          loading={loading}
          className="animate-fadeIn"
        />
      ))}
    </div>
  );
};

export default HomePage;
