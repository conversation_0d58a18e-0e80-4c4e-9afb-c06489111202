// Sample game data for development and testing purposes
// In a production environment, this data would come from an API

// Helper function to generate a unique ID
const generateId = (index, prefix = "game") => `${prefix}_${index + 1}`;

// Base games that will be used in both sampleGames and moreSampleGames
const baseGames = [
  {
    title: "Cyber Nexus",
    genre: "Action",
    tags: ["Cyberpunk", "Shooter", "RPG", "Singleplayer"],
    description: "A cyberpunk action game with RPG elements set in a dystopian future.",
    paymentType: "paid",
    price: "$19.99",
    developer: "NeonByte Studios",
    releaseDate: "2023-04-15",
    image: "https://via.placeholder.com/600x400/0A0A0A/FF4500?text=Cyber+Nexus"
  },
  {
    title: "Mystic Journey",
    genre: "Adventure",
    tags: ["Fantasy", "Story-Rich", "Puzzle", "Magic"],
    description: "Embark on a magical journey through an enchanted world filled with puzzles and mysteries.",
    paymentType: "paid",
    price: "$24.99",
    developer: "Enchanted Games",
    releaseDate: "2023-02-28",
    image: "https://via.placeholder.com/600x400/0A0A0A/4A65FF?text=Mystic+Journey"
  },
  {
    title: "Pixel Survivors",
    genre: "Survival",
    tags: ["Pixel Art", "Roguelike", "Survival", "2D"],
    description: "A retro-style survival game where every pixel matters. Survive as long as possible!",
    paymentType: "free",
    developer: "Pixelated Dreams",
    releaseDate: "2023-05-10",
    image: "https://via.placeholder.com/600x400/0A0A0A/00B09B?text=Pixel+Survivors"
  },
  {
    title: "Space Explorers",
    genre: "Simulation",
    tags: ["Space", "Sandbox", "Exploration", "Sci-Fi"],
    description: "Explore the vastness of space, build ships, and discover new planets in this open-world space simulator.",
    paymentType: "paid",
    price: "$29.99",
    developer: "Cosmic Games",
    releaseDate: "2023-01-20",
    image: "https://via.placeholder.com/600x400/0A0A0A/FF8C00?text=Space+Explorers"
  },
  {
    title: "Rhythm Masters",
    genre: "Music",
    tags: ["Rhythm", "Music", "Arcade", "Multiplayer"],
    description: "Test your rhythm in this fast-paced music game with over 100 tracks to master.",
    paymentType: "free",
    developer: "Beat Box Games",
    releaseDate: "2022-11-05",
    image: "https://via.placeholder.com/600x400/0A0A0A/FF69B4?text=Rhythm+Masters"
  },
  {
    title: "Tactical Heroes",
    genre: "Strategy",
    tags: ["Turn-Based", "Tactical", "War", "Military"],
    description: "Command your elite squad in this tactical turn-based strategy game.",
    paymentType: "paid",
    price: "$14.99",
    developer: "Strategy Minds",
    releaseDate: "2023-03-12",
    image: "https://via.placeholder.com/600x400/0A0A0A/FFC107?text=Tactical+Heroes"
  },
  {
    title: "Neon Racer",
    genre: "Racing",
    tags: ["Arcade", "Multiplayer", "Neon", "Fast-Paced"],
    description: "Race through neon-lit streets in futuristic vehicles at breakneck speeds.",
    paymentType: "credits",
    developer: "Velocity Studios",
    releaseDate: "2023-06-02",
    image: "https://via.placeholder.com/600x400/0A0A0A/FF1493?text=Neon+Racer"
  },
  {
    title: "Castle Defense",
    genre: "Tower Defense",
    tags: ["Strategy", "Medieval", "Defense", "Resource Management"],
    description: "Build and defend your castle against waves of invading enemies.",
    paymentType: "free",
    developer: "Kingdom Games",
    releaseDate: "2022-12-18",
    image: "https://via.placeholder.com/600x400/0A0A0A/8BC34A?text=Castle+Defense"
  },
  {
    title: "Haunted Halls",
    genre: "Horror",
    tags: ["Survival Horror", "First-Person", "Atmospheric", "Puzzle"],
    description: "Explore a haunted mansion filled with terrifying secrets and paranormal activity.",
    paymentType: "paid",
    price: "$16.99",
    developer: "Nightmare Studios",
    releaseDate: "2023-04-30",
    image: "https://via.placeholder.com/600x400/0A0A0A/9C27B0?text=Haunted+Halls"
  },
  {
    title: "Cube World",
    genre: "Sandbox",
    tags: ["Voxel", "Building", "Exploration", "Crafting"],
    description: "Build, craft, and explore in this procedurally generated voxel world.",
    paymentType: "paid",
    price: "$9.99",
    developer: "Block Studios",
    releaseDate: "2023-02-14",
    image: "https://via.placeholder.com/600x400/0A0A0A/4CAF50?text=Cube+World"
  },
  {
    title: "Sky Pirates",
    genre: "Action",
    tags: ["Flying", "Adventure", "Pirates", "Open World"],
    description: "Take to the skies as a daring pirate captain in search of legendary treasures.",
    paymentType: "paid",
    price: "$22.99",
    developer: "Horizon Games",
    releaseDate: "2023-05-25",
    image: "https://via.placeholder.com/600x400/0A0A0A/3F51B5?text=Sky+Pirates"
  },
  {
    title: "Dungeon Crawler",
    genre: "RPG",
    tags: ["Dungeon Crawler", "Roguelike", "Pixel Art", "Difficult"],
    description: "Delve deep into procedurally generated dungeons filled with monsters and loot.",
    paymentType: "free",
    developer: "Crypt Games",
    releaseDate: "2023-01-08",
    image: "https://via.placeholder.com/600x400/0A0A0A/795548?text=Dungeon+Crawler"
  }
];

// First set of games for the main display
const createSampleGames = () => {
  const freeGames = baseGames
    .filter(game => game.paymentType === "free")
    .slice(0, 6)
    .map((game, index) => ({ ...game, id: generateId(index, "free") }));

  const topGames = baseGames
    .filter(game => game.paymentType === "paid")
    .slice(0, 6)
    .map((game, index) => ({ ...game, id: generateId(index, "top") }));

  const newReleases = baseGames
    .sort((a, b) => new Date(b.releaseDate) - new Date(a.releaseDate))
    .slice(0, 6)
    .map((game, index) => ({ ...game, id: generateId(index, "new") }));

  return { freeGames, topGames, newReleases };
};

// Additional games for the "Show All" expanded sections
const createMoreSampleGames = () => {
  // Create variations of the base games by modifying titles and other properties
  const variations = baseGames.map(game => {
    const variations = [
      { titlePrefix: "Super ", priceModifier: 1.2 },
      { titlePrefix: "Ultimate ", priceModifier: 1.5 },
      { titlePrefix: "Epic ", priceModifier: 1.3 },
      { titlePrefix: "Legendary ", priceModifier: 1.6 },
      { titlePrefix: "Classic ", priceModifier: 0.8 },
      { titlePrefix: "Modern ", priceModifier: 1.1 }
    ];

    // Create multiple variations of each base game
    return variations.map((variant, varIndex) => {
      const newGame = { ...game };
      
      // Modify title with variant prefix
      newGame.title = variant.titlePrefix + game.title;
      
      // Adjust price if it's a paid game
      if (newGame.paymentType === "paid" && newGame.price) {
        const originalPrice = parseFloat(newGame.price.replace(/[^0-9.]/g, ''));
        newGame.price = `$${(originalPrice * variant.priceModifier).toFixed(2)}`;
      }
      
      // Add some additional tags
      const additionalTags = ["Indie", "Early Access", "Casual", "Competitive", "Relaxing"];
      if (newGame.tags && newGame.tags.length < 5) {
        newGame.tags = [...newGame.tags, additionalTags[varIndex % additionalTags.length]];
      }
      
      // Adjust release date to be more recent for some variations
      const releaseDate = new Date(newGame.releaseDate);
      releaseDate.setDate(releaseDate.getDate() + (varIndex * 7)); // Offset by weeks
      newGame.releaseDate = releaseDate.toISOString().split('T')[0];
      
      return newGame;
    });
  });

  // Flatten and map with IDs
  const allVariations = variations.flat();
  
  // Create larger sets of games for each category
  const freeGames = [
    ...baseGames.filter(game => game.paymentType === "free"),
    ...allVariations.filter(game => game.paymentType === "free")
  ].map((game, index) => ({ ...game, id: generateId(index + 100, "free") }));
  
  const topGames = [
    ...baseGames.filter(game => game.paymentType === "paid"),
    ...allVariations.filter(game => game.paymentType === "paid")
  ].map((game, index) => ({ ...game, id: generateId(index + 100, "top") }));
  
  const newReleases = [
    ...baseGames,
    ...allVariations.slice(0, 20)
  ]
    .sort((a, b) => new Date(b.releaseDate) - new Date(a.releaseDate))
    .map((game, index) => ({ ...game, id: generateId(index + 100, "new") }));

  return { freeGames, topGames, newReleases };
};

export const sampleGames = createSampleGames();
export const moreSampleGames = createMoreSampleGames();
