// Upload configuration constants
export const UPLOAD_CONFIG = {
  // Maximum file size in bytes (1GB = 1024 * 1024 * 1024)
  MAX_FILE_SIZE: 1024 * 1024 * 1024, // 1GB
  
  // Maximum file size in MB for display purposes
  MAX_FILE_SIZE_MB: 1024,
  
  // Minimum description length
  MIN_DESCRIPTION_LENGTH: 50,
  
  // Allowed game file formats (web games only)
  ALLOWED_GAME_FORMATS: ['.zip'],
  
  // Required files in ZIP for web games
  REQUIRED_ZIP_FILES: {
    INDEX_HTML: 'index.html',
    COMPRESSION_EXTENSIONS: ['.gzip', '.gz', '.br']
  },
  
  // Allowed web game types
  ALLOWED_WEB_GAME_TYPES: ['html5', 'webgl', 'unity'],
  
  // Upload chunk size for progress tracking (8MB chunks)
  UPLOAD_CHUNK_SIZE: 8 * 1024 * 1024
};

// Helper function to format file size
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Helper function to validate file size
export const validateFileSize = (file) => {
  return file.size <= UPLOAD_CONFIG.MAX_FILE_SIZE;
};

// Helper function to validate file format
export const validateGameFileFormat = (file) => {
  const extension = '.' + file.name.split('.').pop().toLowerCase();
  return UPLOAD_CONFIG.ALLOWED_GAME_FORMATS.includes(extension);
};
