/**
 * Environment configuration - Single source of truth for all environment variables
 * @description This module provides a centralized way to access environment variables
 * and ensures consistent configuration across the application.
 */

// Helper function to get safe default API URL
const getDefaultApiUrl = () => {
  if (typeof window !== 'undefined') {
    // In browser, use current protocol to avoid mixed content
    const protocol = window.location.protocol;
    const hostname = window.location.hostname;
    
    // For production, use relative URL
    if (hostname !== 'localhost' && hostname !== '127.0.0.1') {
      return '/api';
    }
    
    // For localhost development
    return `${protocol}//localhost:3000/api`;
  }
  // Server-side or build time fallback
  return 'http://localhost:3000/api';
};

// Environment variables with defaults
export const ENV_CONFIG = {
  // API Configuration
  API_URL: import.meta.env.VITE_API_URL || getDefaultApiUrl(),
  
  // Auth Configuration
  PAYPAL_CLIENT_ID: import.meta.env.VITE_PAYPAL_CLIENT_ID,
  GOOGLE_CLIENT_ID: import.meta.env.VITE_GOOGLE_CLIENT_ID,
  DISCORD_CLIENT_ID: import.meta.env.VITE_DISCORD_CLIENT_ID,
  DISCORD_REDIRECT_URI: import.meta.env.VITE_DISCORD_REDIRECT_URI,
  
  // Environment Information
  NODE_ENV: import.meta.env.MODE,
  IS_DEVELOPMENT: import.meta.env.DEV,
  IS_PRODUCTION: import.meta.env.PROD,
  
  // S3 Configuration for media storage
  S3_URL: import.meta.env.VITE_S3_URL || null,
  MEDIA_BASE_URL: import.meta.env.VITE_MEDIA_BASE_URL,
  USE_S3: import.meta.env.PROD, // Use S3 in production by default
  
  // Base URL (without /api)
  BASE_URL: (import.meta.env.VITE_API_URL || getDefaultApiUrl()).replace('/api', ''),
};

// Export individual values for convenience
export const {
  API_URL,
  BASE_URL,
  PAYPAL_CLIENT_ID,
  GOOGLE_CLIENT_ID,
  DISCORD_CLIENT_ID,
  DISCORD_REDIRECT_URI,
  NODE_ENV,
  IS_DEVELOPMENT,
  IS_PRODUCTION,
  // S3 Configuration
  S3_URL,
  MEDIA_BASE_URL,
  USE_S3,
} = ENV_CONFIG;

// Helper function to get base URL (without /api)
export const getBaseUrl = () => API_URL.replace('/api', '');

export default ENV_CONFIG;