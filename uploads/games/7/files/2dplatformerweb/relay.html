<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Unity WebGL Game</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      background-color: #000;
    }
    #unity-container {
      width: 100%;
      height: 100%;
      background: #000;
      position: absolute;
    }
    #unity-canvas {
      width: 100%;
      height: 100%;
      background: #000;
    }
    #loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #000;
      color: white;
      z-index: 100;
    }
    .spinner {
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    #error-message {
      color: red;
      max-width: 80%;
      text-align: center;
      margin-top: 20px;
      display: none;
    }
    .debug-info {
      font-size: 12px;
      opacity: 0.7;
      max-width: 80%;
      margin-top: 20px;
      overflow-wrap: break-word;
    }
    #fullscreen-button {
      position: absolute;
      bottom: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.5);
      border: 2px solid rgba(255, 255, 255, 0.5);
      color: white;
      border-radius: 5px;
      padding: 10px 15px;
      cursor: pointer;
      font-size: 14px;
      font-family: Arial, sans-serif;
      display: flex;
      align-items: center;
      z-index: 10;
      transition: all 0.3s ease;
      opacity: 0.7;
    }
    
    #fullscreen-button:hover {
      background: rgba(0, 0, 0, 0.7);
      opacity: 1;
    }
    
    #fullscreen-button svg {
      width: 16px;
      height: 16px;
      margin-right: 5px;
    }
    
    #fullscreen-button.hidden {
      display: none;
    }
  </style>
</head>
<body>
  <div id="unity-container">
    <div id="loading-overlay">
      <div class="spinner"></div>
      <p>Loading Unity WebGL Game...</p>
      <div id="error-message"></div>
    </div>
    <canvas id="unity-canvas"></canvas>
    
    <!-- Pulsante Fullscreen -->
    <button id="fullscreen-button" aria-label="Fullscreen">
      <svg viewBox="0 0 24 24" fill="white">
        <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
      </svg>
      Fullscreen
    </button>
  </div>

  <script>
    // Important: We're using ONLY gzipped files as that's what we have
    var buildUrl = "Build";
    var loaderUrl = buildUrl + "/2dplatformerweb.loader.js";
    var config = {
      dataUrl: buildUrl + "/2dplatformerweb.data.gz", // Server will decompress this
      frameworkUrl: buildUrl + "/2dplatformerweb.framework.js.gz", // Server will decompress this
      codeUrl: buildUrl + "/2dplatformerweb.wasm.gz", // Server will decompress this
      streamingAssetsUrl: "StreamingAssets",
      companyName: "IndieRepo",
      productName: "2D Platformer",
      productVersion: "1.0",
    };

    var loadingOverlay = document.querySelector("#loading-overlay");
    var errorMessage = document.querySelector("#error-message");
    var canvas = document.querySelector("#unity-canvas");
    var fullscreenButton = document.getElementById('fullscreen-button');
    var container = document.getElementById('unity-container');
    
    function displayError(message) {
      errorMessage.style.display = "block";
      errorMessage.innerHTML = message;
      fullscreenButton.classList.add('hidden'); // Nascondi il pulsante in caso di errore
    }

    function loadUnityGame() {
      var script = document.createElement("script");
      script.src = loaderUrl;
      
      script.onload = () => {
        // Check if createUnityInstance exists
        if (typeof createUnityInstance !== 'function') {
          displayError("Unity WebGL loader did not define createUnityInstance function. This may be due to an incompatible Unity version.");
          return;
        }
        
        // Try to create the Unity instance
        createUnityInstance(canvas, config, (progress) => {
          // Update loading progress if needed
          if (progress === 1) {
            // Hide loading overlay when game is fully loaded
            loadingOverlay.style.display = "none";
            // Mostra il pulsante fullscreen quando il gioco è caricato
            fullscreenButton.classList.remove('hidden');
          }
        }).catch((error) => {
          console.error("Unity WebGL error:", error);
          displayError("Error loading the Unity WebGL game: " + error + "<br><br>" +
                       "Please try refreshing the page or try a different browser.");
          
          // Add debug info
          var debugInfo = document.createElement("div");
          debugInfo.className = "debug-info";
          debugInfo.innerHTML = "Technical details:<br>" + 
                              "Browser: " + navigator.userAgent + "<br>" +
                              "Error: " + error + "<br>" +
                              "Load attempt: Gzipped files";
          errorMessage.appendChild(debugInfo);
          
          // Add alternative link to play in a new window
          var playLink = document.createElement("a");
          playLink.href = window.location.href;
          playLink.target = "_blank";
          playLink.style.display = "inline-block";
          playLink.style.marginTop = "20px";
          playLink.style.padding = "10px 20px";
          playLink.style.backgroundColor = "#4a90e2";
          playLink.style.color = "white";
          playLink.style.borderRadius = "4px";
          playLink.style.textDecoration = "none";
          playLink.innerHTML = "Try in New Window";
          errorMessage.appendChild(playLink);
        });
      };
      
      script.onerror = () => {
        displayError("Failed to load Unity WebGL loader script. Please check your internet connection and try again.");
      };
      
      document.body.appendChild(script);
    }
    
    // Aggiunta della funzionalità di fullscreen
    fullscreenButton.addEventListener('click', function() {
      // Controllo se siamo già in fullscreen
      if (document.fullscreenElement || 
          document.webkitFullscreenElement || 
          document.mozFullScreenElement ||
          document.msFullscreenElement) {
        
        // Uscita dalla modalità fullscreen
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitExitFullscreen) { // Safari
          document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) { // Firefox
          document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) { // IE/Edge
          document.msExitFullscreen();
        }
        return;
      }
      
      // Controllo se il fullscreen è supportato
      if (document.fullscreenEnabled || 
          document.webkitFullscreenEnabled || 
          document.mozFullScreenEnabled ||
          document.msFullscreenEnabled) {
        
        // Richiesta di fullscreen per il container
        if (container.requestFullscreen) {
          container.requestFullscreen();
        } else if (container.webkitRequestFullscreen) { // Safari
          container.webkitRequestFullscreen();
        } else if (container.mozRequestFullScreen) { // Firefox
          container.mozRequestFullScreen();
        } else if (container.msRequestFullscreen) { // IE/Edge
          container.msRequestFullscreen();
        }
      }
    });
    
    // Aggiorna il testo del pulsante quando cambia lo stato del fullscreen
    document.addEventListener('fullscreenchange', updateFullscreenButtonText);
    document.addEventListener('webkitfullscreenchange', updateFullscreenButtonText);
    document.addEventListener('mozfullscreenchange', updateFullscreenButtonText);
    document.addEventListener('MSFullscreenChange', updateFullscreenButtonText);
    
    function updateFullscreenButtonText() {
      if (document.fullscreenElement || 
          document.webkitFullscreenElement || 
          document.mozFullScreenElement ||
          document.msFullscreenElement) {
        // Siamo in modalità fullscreen
        fullscreenButton.innerHTML = `
          <svg viewBox="0 0 24 24" fill="white">
            <path d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"/>
          </svg>
          Exit Fullscreen`;
      } else {
        // Non siamo in fullscreen
        fullscreenButton.innerHTML = `
          <svg viewBox="0 0 24 24" fill="white">
            <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
          </svg>
          Fullscreen`;
      }
    }
    
    // Nascondi inizialmente il pulsante fullscreen fino a quando il gioco non è caricato
    fullscreenButton.classList.add('hidden');
    
    // Start loading the game
    loadUnityGame();
  </script>
</body>
</html>
