-- Create the database
CREATE DATABASE IF NOT EXISTS indierepo;
USE indierepo;

-- Users table (updated with Discord auth support)
CREATE TABLE IF NOT EXISTS Users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(30) NOT NULL UNIQUE,
  email VARCHAR(100) NOT NULL UNIQUE,
  password VARCHAR(100) NULL, -- Changed to NULL to allow social login users without password
  googleId VARCHAR(255) UNIQUE, -- Added for Google authentication
  discordId VARCHAR(255) UNIQUE, -- Added for Discord authentication
  profileImage VARCHAR(255),
  displayName VARCHAR(100),
  bio TEXT NULL, -- Added bio field for user profile
  isGoogleUser BOOLEAN DEFAULT FALSE,
  isVerified BOOLEAN DEFAULT FALSE,
  provider VARCHAR(20) DEFAULT 'local',
  role VARCHAR(20) DEFAULT 'user',
  credits INT DEFAULT 0,
  referredBy INT NULL,
  referralCode VARCHAR(20) UNIQUE,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (referredBy) REFERENCES Users(id) ON DELETE SET NULL
);

-- New table for user avatars (similar to how GameImages works)
CREATE TABLE IF NOT EXISTS UserAvatars (
  id INT AUTO_INCREMENT PRIMARY KEY,
  userId INT NOT NULL,
  fileName VARCHAR(255) NOT NULL,
  filePath VARCHAR(255) NOT NULL,
  fileSize INT NOT NULL, -- Size in KB
  width INT,
  height INT,
  isActive BOOLEAN DEFAULT TRUE, -- Flag to indicate the currently active avatar
  s3Key VARCHAR(500) NULL COMMENT 'AWS S3 object key for avatar operations',
  uploadedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES Users(id) ON DELETE CASCADE
);

-- Games table (updated with file references, userId, and genre/tags)
CREATE TABLE IF NOT EXISTS Games (
  id INT AUTO_INCREMENT PRIMARY KEY,
  userId INT NOT NULL,  -- Added to track the game uploader
  title VARCHAR(100) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL, -- Add slug column for subdomain access
  description TEXT,
  genre VARCHAR(50) NOT NULL,
  tags VARCHAR(255) NOT NULL,  -- Store comma-separated tags
  priceModel ENUM('free', 'paid', 'credits') DEFAULT 'free',
  price DECIMAL(10,2) DEFAULT 0.00,
  creditPrice INT DEFAULT 0,
  mainVideoUrl VARCHAR(255),  -- Store main video URL
  steamUrl VARCHAR(255),
  itchUrl VARCHAR(255),
  epicGamesUrl VARCHAR(255),
  isWebGame BOOLEAN DEFAULT FALSE,  -- Indicates if this is a web-playable game
  webGameUrl VARCHAR(255),  -- URL for directly playable web games if hosted externally
  webGameType ENUM('webgl', 'html5', 'unity', 'construct', 'phaserjs', 'gamemaker', 'godot', 'other') NULL,  -- Type of web game technology
  hasEmbeddedVersion BOOLEAN DEFAULT FALSE,  -- Whether the game has a playable embedded version
  downloadCount INT DEFAULT 0,
  viewCount INT DEFAULT 0,
  status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
  releaseDate DATETIME DEFAULT CURRENT_TIMESTAMP,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES Users(id) ON DELETE CASCADE,
  
  -- Payment related fields
  paymentEnabled BOOLEAN DEFAULT FALSE,
  paypalClientId VARCHAR(255), -- Client ID for PayPal SDK initialization
  googlePayClientId VARCHAR(255), -- Client ID for Google Pay API initialization
  stripePublishableKey VARCHAR(255), -- Publishable key for Stripe integration
  paymentMethodsEnabled JSON, -- Stores array of enabled payment methods: ["paypal", "googlepay", "stripe", etc.]
  
  -- Revenue sharing options
  revenueSharingEnabled BOOLEAN DEFAULT FALSE,
  revenueSharePercentage DECIMAL(5, 2) DEFAULT 0
);

-- Game Files table (updated to allow NULL filePath for external links)
CREATE TABLE IF NOT EXISTS GameFiles (
  id INT AUTO_INCREMENT PRIMARY KEY,
  gameId INT NOT NULL,
  fileName VARCHAR(255) NOT NULL,
  filePath VARCHAR(255) NULL,  -- Changed from NOT NULL to NULL to support external links
  fileSize INT NOT NULL,  -- Size in KB
  fileType VARCHAR(50) NOT NULL,  -- Adding web-specific types: 'game', 'demo', 'mod', 'tools', 'web-build', 'web-embed', 'source', 'other'
  description VARCHAR(255) DEFAULT '',  -- Description of what this file contains
  requiresPurchase BOOLEAN DEFAULT FALSE,  -- Indicates if this file requires payment to access
  isExternalLink BOOLEAN DEFAULT FALSE,  -- Indicates if this is an external download link
  externalUrl VARCHAR(255) DEFAULT '',   -- URL for external downloads (Dropbox, Google Drive, etc.)
  isWebPlayable BOOLEAN DEFAULT FALSE,  -- Indicates if this file is directly playable in browser
  webEntryPoint VARCHAR(255) DEFAULT '',  -- Entry point HTML file for web builds (e.g., 'index.html')
  version VARCHAR(20) DEFAULT '1.0',
  s3Key VARCHAR(500) NULL COMMENT 'AWS S3 object key for file operations',
  uploadedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (gameId) REFERENCES Games(id) ON DELETE CASCADE,
  CONSTRAINT check_file_path CHECK ((isExternalLink = FALSE AND filePath IS NOT NULL) OR 
                                    (isExternalLink = TRUE))
);

-- Game Images table (new)
CREATE TABLE IF NOT EXISTS GameImages (
  id INT AUTO_INCREMENT PRIMARY KEY,
  gameId INT NOT NULL,
  imageType ENUM('cover', 'card', 'gif', 'screenshot') NOT NULL,  -- Type of image (added screenshot)
  fileName VARCHAR(255) NOT NULL,
  filePath VARCHAR(255) NOT NULL,
  fileSize INT NOT NULL,  -- Size in KB
  width INT,
  height INT,
  s3Key VARCHAR(500) NULL COMMENT 'AWS S3 object key for image operations',
  uploadedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (gameId) REFERENCES Games(id) ON DELETE CASCADE,
  UNIQUE KEY unique_image_type (gameId, imageType)  -- Only one of each type per game
);

-- Game Screenshots table (new)
CREATE TABLE IF NOT EXISTS GameScreenshots (
  id INT AUTO_INCREMENT PRIMARY KEY,
  gameId INT NOT NULL,
  fileName VARCHAR(255) NOT NULL,
  filePath VARCHAR(255) NOT NULL,
  fileSize INT NOT NULL,  -- Size in KB
  width INT,
  height INT,
  displayOrder INT DEFAULT 0,  -- For ordering screenshots
  uploadedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (gameId) REFERENCES Games(id) ON DELETE CASCADE
);

-- Game Videos table (new)
CREATE TABLE IF NOT EXISTS GameVideos (
  id INT AUTO_INCREMENT PRIMARY KEY,
  gameId INT NOT NULL,
  videoUrl VARCHAR(255) NOT NULL,
  videoType VARCHAR(20) NOT NULL,  -- 'youtube', 'vimeo', etc.
  videoId VARCHAR(50) NOT NULL,  -- The actual ID from the platform
  displayOrder INT DEFAULT 0,  -- 0 for main video, 1+ for additional
  addedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (gameId) REFERENCES Games(id) ON DELETE CASCADE
);

-- Referral transactions table (new)
CREATE TABLE IF NOT EXISTS ReferralTransactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  referrerId INT NOT NULL,
  referredId INT NOT NULL,
  creditsAwarded INT DEFAULT 0,
  status ENUM('pending', 'completed', 'rejected') DEFAULT 'pending',
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (referrerId) REFERENCES Users(id),
  FOREIGN KEY (referredId) REFERENCES Users(id)
);

-- Credit transactions table (new)
CREATE TABLE IF NOT EXISTS CreditTransactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  userId INT NOT NULL,
  amount INT NOT NULL,  -- Can be positive (earned) or negative (spent)
  description VARCHAR(255),
  transactionType ENUM('purchase', 'referral', 'reward', 'admin', 'other') NOT NULL,
  gameId INT NULL,  -- If transaction is related to a game
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES Users(id),
  FOREIGN KEY (gameId) REFERENCES Games(id)
);

-- Reviews table (with likesCount, dislikesCount e commentCount già inclusi)
CREATE TABLE IF NOT EXISTS Reviews (
  id INT AUTO_INCREMENT PRIMARY KEY,
  userId INT NOT NULL,
  gameId INT NOT NULL,
  rating INT CHECK (rating >= 1 AND rating <= 5),
  title VARCHAR(255) DEFAULT 'Review',
  comment TEXT,
  likesCount INT DEFAULT 0,
  dislikesCount INT DEFAULT 0,
  commentCount INT DEFAULT 0,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES Users(id),
  FOREIGN KEY (gameId) REFERENCES Games(id)
);

-- Create a new table to track review reactions (likes/dislikes)
CREATE TABLE IF NOT EXISTS ReviewReactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  userId INT NOT NULL,
  reviewId INT NOT NULL,
  reactionType ENUM('like', 'dislike') NOT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES Users(id) ON DELETE CASCADE,
  FOREIGN KEY (reviewId) REFERENCES Reviews(id) ON DELETE CASCADE,
  UNIQUE KEY unique_user_review_reaction (userId, reviewId)
);

-- User_Games (for favorites/library)
CREATE TABLE IF NOT EXISTS User_Games (
  userId INT NOT NULL,
  gameId INT NOT NULL,
  isFavorite BOOLEAN DEFAULT FALSE,
  inLibrary BOOLEAN DEFAULT FALSE,
  purchaseType ENUM('free', 'paid', 'credits') NULL,  -- How the game was acquired
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (userId, gameId),
  FOREIGN KEY (userId) REFERENCES Users(id),
  FOREIGN KEY (gameId) REFERENCES Games(id)
);

-- Create a new table for review comments
CREATE TABLE IF NOT EXISTS ReviewComments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  reviewId INT NOT NULL,
  userId INT NOT NULL,
  content TEXT NOT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (reviewId) REFERENCES Reviews(id) ON DELETE CASCADE,
  FOREIGN KEY (userId) REFERENCES Users(id) ON DELETE CASCADE
);

-- Purchases table to track game purchases
CREATE TABLE Purchases (
  id INT AUTO_INCREMENT PRIMARY KEY,
  gameId INT NOT NULL,
  userId INT NOT NULL,
  transactionId VARCHAR(255) NOT NULL,
  paymentMethod ENUM('paypal', 'googlepay', 'stripe', 'credits') NOT NULL,
  amount DECIMAL(10, 2) NOT NULL,
  currency VARCHAR(10) DEFAULT 'USD',
  status ENUM('pending', 'completed', 'refunded', 'failed') DEFAULT 'pending',
  purchaseDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  refundDate TIMESTAMP NULL,
  FOREIGN KEY (gameId) REFERENCES Games(id) ON DELETE CASCADE,
  FOREIGN KEY (userId) REFERENCES Users(id) ON DELETE CASCADE,
  UNIQUE KEY unique_transaction (transactionId)
);

-- Payment Processing History
CREATE TABLE PaymentHistory (
  id INT AUTO_INCREMENT PRIMARY KEY,
  purchaseId INT,
  rawPaymentData JSON, -- Stores full payment response from provider
  paymentProvider ENUM('paypal', 'googlepay', 'stripe', 'internal') NOT NULL,
  paymentProviderId VARCHAR(255), -- ID from the payment provider
  processingDetails JSON, -- Details about processing steps
  errorMessage TEXT,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (purchaseId) REFERENCES Purchases(id) ON DELETE SET NULL
);

-- Payment Payouts to Game Creators
CREATE TABLE PayoutRequests (
  id INT AUTO_INCREMENT PRIMARY KEY,
  userId INT NOT NULL, -- The game creator requesting payout
  amount DECIMAL(10, 2) NOT NULL,
  currency VARCHAR(10) DEFAULT 'USD',
  status ENUM('pending', 'processing', 'completed', 'rejected') DEFAULT 'pending',
  paymentMethod ENUM('paypal', 'bank_transfer', 'check') NOT NULL,
  paymentDetails JSON, -- Payment details like PayPal email or bank info
  requestDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  processedDate TIMESTAMP NULL,
  FOREIGN KEY (userId) REFERENCES Users(id) ON DELETE CASCADE
);

-- User Payment Settings
CREATE TABLE UserPaymentSettings (
  userId INT PRIMARY KEY,
  paypalEmail VARCHAR(255),
  paypalClientId VARCHAR(255), -- Client ID for PayPal integration
  googlePayClientId VARCHAR(255), -- Client ID for Google Pay integration
  stripePublishableKey VARCHAR(255), -- Publishable key for Stripe integration
  bankAccountInfo JSON, -- Encrypted bank account information
  preferredPayoutMethod ENUM('paypal', 'bank_transfer', 'check') DEFAULT 'paypal',
  minimumPayoutAmount DECIMAL(10, 2) DEFAULT 20.00,
  autoPayoutEnabled BOOLEAN DEFAULT FALSE,
  lastUpdated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES Users(id) ON DELETE CASCADE
);

-- System Configuration table for tracking migrations and system settings
CREATE TABLE IF NOT EXISTS SystemConfig (
  id INT AUTO_INCREMENT PRIMARY KEY,
  config_key VARCHAR(100) NOT NULL UNIQUE,
  config_value TEXT,
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert initial system configuration
INSERT INTO SystemConfig (config_key, config_value, description) VALUES
('db_version', '1.0', 'Current database schema version'),
('s3_migration_status', 'not_started', 'Status of S3 migration (not_started, in_progress, completed)'),
('storage_backend', 'local', 'Current storage backend (local, s3, hybrid)');

-- Indexes for performance
CREATE INDEX idx_games_priceModel ON Games(priceModel);
CREATE INDEX idx_games_publisher ON Games(userId); -- Fixed: changed publisherId to userId
CREATE INDEX idx_purchases_user ON Purchases(userId);
CREATE INDEX idx_purchases_game ON Purchases(gameId);
CREATE INDEX idx_purchases_date ON Purchases(purchaseDate);
CREATE INDEX idx_games_payment ON Games(paymentEnabled);

-- S3-related indexes for performance
CREATE INDEX idx_gamefiles_s3key ON GameFiles(s3Key);
CREATE INDEX idx_gameimages_s3key ON GameImages(s3Key);
CREATE INDEX idx_useravatars_s3key ON UserAvatars(s3Key);
CREATE INDEX idx_systemconfig_key ON SystemConfig(config_key);

-- Views
CREATE VIEW GamePurchaseStats AS
SELECT 
  g.id as gameId, 
  g.title, 
  COUNT(p.id) as totalPurchases, 
  SUM(p.amount) as totalRevenue,
  g.userId -- Fixed: changed publisherId to userId
FROM Games g
LEFT JOIN Purchases p ON g.id = p.gameId AND p.status = 'completed'
GROUP BY g.id, g.title, g.userId; -- Fixed: changed publisherId to userId

-- Trigger to automatically update revenueSharingEnabled when percentage is set
DELIMITER //
CREATE TRIGGER update_revenue_sharing 
BEFORE UPDATE ON Games
FOR EACH ROW
BEGIN
  IF NEW.revenueSharePercentage > 0 THEN
    SET NEW.revenueSharingEnabled = TRUE;
  ELSE 
    SET NEW.revenueSharingEnabled = FALSE;
  END IF;
END//
DELIMITER ;