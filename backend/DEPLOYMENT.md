# IndieRepo Backend Deployment Guide

## Automatic Database Migration on AWS EC2

This backend is configured to automatically run database migrations when deployed to AWS EC2 via GitHub Actions.

## Migration Process

### What Happens During Deployment

1. **Code Deployment**: Latest code is deployed to EC2 instance
2. **Dependencies Installation**: Production dependencies are installed
3. **Database Migration**: Knex migrations are automatically executed
4. **Health Check**: Database connection and schema are verified
5. **Service Restart**: Backend service is restarted with PM2

### Migration Commands Available

- `npm run migrate` - Run migrations in development
- `npm run migrate:prod` - Run migrations in production
- `npm run migrate:status` - Check migration status (development)
- `npm run migrate:status:prod` - Check migration status (production)
- `npm run migrate:rollback` - Rollback last migration (development)
- `npm run migrate:rollback:prod` - Rollback last migration (production)
- `npm run health-check` - Verify database health and migration status
- `npm run deploy` - Full deployment command (install + migrate)

### Database Configuration

The application uses PostgreSQL with the following configuration hierarchy:

1. **Development**: Uses local PostgreSQL or environment variables
2. **Production**: Uses AWS RDS PostgreSQL with SSL connection

### Environment Variables

Required environment variables for production:

```bash
DB_HOST=your-rds-endpoint.amazonaws.com
DB_PORT=5432
DB_NAME=indierepo
DB_USER=your-db-user
DB_PASSWORD=your-db-password
NODE_ENV=production
```

### Migration Files Location

- Migrations are stored in `backend/migrations/`
- All migration files are automatically included in deployment packages
- Migrations are executed in chronological order based on filename

### Rollback Strategy

If a migration fails during deployment:

1. **Automatic Handling**: Deployment continues but logs warnings
2. **Manual Rollback**: SSH into EC2 and run:
   ```bash
   cd /var/www/indierepo/backend
   npm run migrate:rollback:prod
   ```
3. **Health Check**: Run health check to verify status:
   ```bash
   npm run health-check
   ```

### Monitoring Migration Status

To check migration status on production:

1. **SSH into EC2 instance**
2. **Navigate to backend directory**:
   ```bash
   cd /var/www/indierepo/backend
   ```
3. **Check migration status**:
   ```bash
   npm run migrate:status:prod
   ```
4. **Run health check**:
   ```bash
   npm run health-check
   ```

### Creating New Migrations

To create a new migration:

```bash
# In development
npm run migrate:make migration_name

# Example
npm run migrate:make add_user_preferences_table
```

### Migration Best Practices

1. **Test Locally**: Always test migrations in development first
2. **Backup Data**: Ensure database backups are in place
3. **Reversible Changes**: Write migrations that can be rolled back
4. **Small Changes**: Keep migrations focused on single changes
5. **Index Management**: Add indexes in separate migrations for performance

### Troubleshooting

#### Migration Fails on Deployment

1. Check EC2 deployment logs: `/var/log/indierepo-deploy.log`
2. Verify database connectivity from EC2
3. Check migration file syntax
4. Verify database user permissions

#### Database Connection Issues

1. Verify RDS security group allows inbound from EC2
2. Check EC2 security group allows outbound to RDS port 5432
3. Verify database credentials in environment variables
4. Check RDS instance status

#### Health Check Failures

1. Run health check manually: `npm run health-check`
2. Check individual table existence
3. Verify migration table integrity
4. Check database permissions

### Security Notes

- SSL is enabled for production database connections
- Database credentials are stored in environment variables
- Connection pooling is configured for optimal performance
- Migrations run with the same user as the application

### Performance Considerations

- Migrations run sequentially during deployment
- Large data migrations may increase deployment time
- Database locks may occur during schema changes
- Consider maintenance windows for major migrations

## Contact

For deployment issues or questions, refer to the application logs or contact the development team. 