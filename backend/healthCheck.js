const axios = require('axios');

// Wait for server to start (let's wait 1 second after startup)
setTimeout(async () => {
  try {
    console.log('🏥 Running backend health check...');
    
    // Test the health endpoint
    const healthResponse = await axios.get('http://localhost:3000/api/health');
    console.log('🏥 Health endpoint check:', healthResponse.data);
    
    // Test the user auth
    const dummyToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MX0.SpLUx4T-fCAEosRlTa1oXy1I5DnD59w_-pmngJQFgUg';
    try {
      const userResponse = await axios.get('http://localhost:3000/api/users/profile', {
        headers: { 'Authorization': `Bearer ${dummyToken}` }
      });
      console.log('🔑 Auth route check attempted');
    } catch (authError) {
      console.log('🔑 Auth route responded with error (expected):', authError.message);
    }
    
    console.log('🏥 Health checks completed');
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    console.error('This may indicate your backend server is not running correctly.');
  }
}, 1000);
