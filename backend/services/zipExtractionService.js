const AWS = require('aws-sdk');
const JSZip = require('jszip');
const path = require('path');
const { UPLOAD_CONFIG } = require('../config/uploadConfig');

// Configure AWS S3
const s3 = new AWS.S3({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

/**
 * Extract ZIP file from S3 and upload individual files back to S3
 * @param {string} s3Key - The S3 key of the ZIP file
 * @param {string} gameId - The game ID for organizing extracted files
 * @returns {Promise<{success: boolean, extractedFiles: Array, webGameUrl: string, error?: string}>}
 */
const extractZipFromS3 = async (s3Key, gameId) => {
  try {
    console.log(`🔄 Starting ZIP extraction for game ${gameId}, S3 key: ${s3Key}`);
    
    // Download ZIP file from S3
    const zipObject = await s3.getObject({
      Bucket: process.env.AWS_S3_BUCKET_NAME,
      Key: s3Key
    }).promise();
    
    console.log(`📦 Downloaded ZIP file, size: ${zipObject.Body.length} bytes`);
    
    // Load ZIP file
    const zip = new JSZip();
    const zipContent = await zip.loadAsync(zipObject.Body);
    
    // Validate ZIP structure
    const validation = await validateZipStructure(zipContent);
    if (!validation.isValid) {
      throw new Error(`Invalid ZIP structure: ${validation.errors.join(', ')}`);
    }
    
    console.log(`✅ ZIP validation passed`);
    
    // Extract and upload files
    const extractedFiles = [];
    const extractedPath = `${UPLOAD_CONFIG.S3_EXTRACTED_PATH_PREFIX}/${gameId}`;
    
    // Process each file in the ZIP
    for (const [fileName, zipFile] of Object.entries(zipContent.files)) {
      if (!zipFile.dir) { // Skip directories
        try {
          // Get file content as buffer
          const fileContent = await zipFile.async('nodebuffer');
          
          // Determine content type and encoding based on file extension
          const { contentType, contentEncoding } = getContentTypeAndEncoding(fileName);

          // Create S3 key for extracted file
          const extractedFileKey = `${extractedPath}/${fileName}`;

          // Prepare S3 upload parameters
          const uploadParams = {
            Bucket: process.env.AWS_S3_BUCKET_NAME,
            Key: extractedFileKey,
            Body: fileContent,
            ContentType: contentType,
            // Set appropriate cache headers for web game files
            CacheControl: contentType.startsWith('text/html') ? 'no-cache' : 'public, max-age=31536000'
          };

          // Add Content-Encoding for compressed files
          if (contentEncoding) {
            uploadParams.ContentEncoding = contentEncoding;
          }

          // Upload extracted file to S3
          await s3.putObject(uploadParams).promise();
          
          extractedFiles.push({
            fileName,
            s3Key: extractedFileKey,
            size: fileContent.length,
            contentType,
            contentEncoding
          });

          const encodingInfo = contentEncoding ? ` [${contentEncoding}]` : '';
          console.log(`📄 Extracted: ${fileName} (${fileContent.length} bytes)${encodingInfo}`);
        } catch (error) {
          console.error(`❌ Error extracting file ${fileName}:`, error);
          throw error;
        }
      }
    }
    
    // Generate web game URL (pointing to index.html)
    const webGameUrl = `https://${process.env.AWS_S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${extractedPath}/index.html`;
    
    console.log(`🎮 Web game URL: ${webGameUrl}`);
    console.log(`✅ ZIP extraction completed. Extracted ${extractedFiles.length} files`);
    
    return {
      success: true,
      extractedFiles,
      webGameUrl,
      extractedPath
    };
    
  } catch (error) {
    console.error('❌ ZIP extraction failed:', error);
    return {
      success: false,
      extractedFiles: [],
      webGameUrl: null,
      error: error.message
    };
  }
};

/**
 * Validate ZIP file structure for web games
 * @param {JSZip} zipContent - The loaded ZIP content
 * @returns {Promise<{isValid: boolean, errors: string[]}>}
 */
const validateZipStructure = async (zipContent) => {
  const errors = [];
  const fileNames = Object.keys(zipContent.files);
  
  // Check for index.html in root
  const hasIndexHtml = fileNames.some(fileName => {
    const file = zipContent.files[fileName];
    return !file.dir && fileName.toLowerCase() === 'index.html';
  });
  
  if (!hasIndexHtml) {
    errors.push('ZIP file must contain index.html in the root folder');
  }
  
  // Check for compressed files (recommended but not required)
  const hasCompressedFiles = fileNames.some(fileName => {
    const lowerFileName = fileName.toLowerCase();
    return UPLOAD_CONFIG.REQUIRED_ZIP_FILES.COMPRESSION_EXTENSIONS.some(ext => 
      lowerFileName.endsWith(ext)
    );
  });
  
  if (!hasCompressedFiles) {
    console.warn('⚠️ ZIP file does not contain compressed files (.gzip, .gz, .br) - this may affect loading performance');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Get appropriate content type and encoding for file based on extension
 * @param {string} fileName - The file name
 * @returns {Object} - Object with contentType and contentEncoding
 */
const getContentTypeAndEncoding = (fileName) => {
  const lowerFileName = fileName.toLowerCase();

  // Handle compressed Unity WebGL files
  if (lowerFileName.endsWith('.js.gz')) {
    return {
      contentType: 'application/javascript',
      contentEncoding: 'gzip'
    };
  }

  if (lowerFileName.endsWith('.wasm.gz')) {
    return {
      contentType: 'application/wasm',
      contentEncoding: 'gzip'
    };
  }

  if (lowerFileName.endsWith('.data.gz')) {
    return {
      contentType: 'application/octet-stream',
      contentEncoding: 'gzip'
    };
  }

  if (lowerFileName.endsWith('.css.gz')) {
    return {
      contentType: 'text/css',
      contentEncoding: 'gzip'
    };
  }

  if (lowerFileName.endsWith('.html.gz')) {
    return {
      contentType: 'text/html',
      contentEncoding: 'gzip'
    };
  }

  // Handle Brotli compressed files
  if (lowerFileName.endsWith('.js.br')) {
    return {
      contentType: 'application/javascript',
      contentEncoding: 'br'
    };
  }

  if (lowerFileName.endsWith('.wasm.br')) {
    return {
      contentType: 'application/wasm',
      contentEncoding: 'br'
    };
  }

  if (lowerFileName.endsWith('.data.br')) {
    return {
      contentType: 'application/octet-stream',
      contentEncoding: 'br'
    };
  }

  // Handle regular file extensions
  const ext = path.extname(fileName).toLowerCase();
  const contentTypes = {
    '.html': 'text/html',
    '.js': 'application/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.ico': 'image/x-icon',
    '.svg': 'image/svg+xml',
    '.wasm': 'application/wasm',
    '.data': 'application/octet-stream',
    '.unity3d': 'application/octet-stream',
    '.unityweb': 'application/octet-stream',
    '.txt': 'text/plain'
  };

  return {
    contentType: contentTypes[ext] || 'application/octet-stream',
    contentEncoding: null
  };
};

/**
 * Get appropriate content type for file based on extension (legacy function for compatibility)
 * @param {string} fileName - The file name
 * @returns {string} - The MIME type
 */
const getContentType = (fileName) => {
  const { contentType } = getContentTypeAndEncoding(fileName);
  return contentType;
};

/**
 * Clean up extracted files from S3 (for cleanup purposes)
 * @param {string} gameId - The game ID
 * @returns {Promise<boolean>}
 */
const cleanupExtractedFiles = async (gameId) => {
  try {
    const extractedPath = `${UPLOAD_CONFIG.S3_EXTRACTED_PATH_PREFIX}/${gameId}/`;
    
    // List all objects with the prefix
    const listParams = {
      Bucket: process.env.AWS_S3_BUCKET_NAME,
      Prefix: extractedPath
    };
    
    const objects = await s3.listObjectsV2(listParams).promise();
    
    if (objects.Contents.length === 0) {
      return true; // Nothing to delete
    }
    
    // Delete all objects
    const deleteParams = {
      Bucket: process.env.AWS_S3_BUCKET_NAME,
      Delete: {
        Objects: objects.Contents.map(obj => ({ Key: obj.Key }))
      }
    };
    
    await s3.deleteObjects(deleteParams).promise();
    console.log(`🧹 Cleaned up ${objects.Contents.length} extracted files for game ${gameId}`);
    
    return true;
  } catch (error) {
    console.error(`❌ Error cleaning up extracted files for game ${gameId}:`, error);
    return false;
  }
};

module.exports = {
  extractZipFromS3,
  validateZipStructure,
  cleanupExtractedFiles
};
