const sharp = require('sharp');
const path = require('path');

/**
 * Image Processing Service
 * Handles image optimization and resizing for game assets
 */

// Standard dimensions for different image types
const IMAGE_DIMENSIONS = {
  card: { width: 230, height: 130 }, // Exact frontend card size
  cover: { width: 1280, height: 720 }, // 16:9 HD cover image
  thumbnail: { width: 400, height: 225 }, // 16:9 thumbnail
  avatar: { width: 200, height: 200 }, // Square avatar
  screenshot: { width: 1920, height: 1080 } // Full HD screenshot
};

// Quality settings for different image types
const QUALITY_SETTINGS = {
  card: { jpeg: 85, webp: 80, png: 90 },
  cover: { jpeg: 90, webp: 85, png: 95 },
  thumbnail: { jpeg: 80, webp: 75, png: 85 },
  avatar: { jpeg: 85, webp: 80, png: 90 },
  screenshot: { jpeg: 95, webp: 90, png: 100 }
};

/**
 * Process and optimize game card image to exact 230x130 dimensions
 * @param {<PERSON>uff<PERSON>} inputBuffer - Original image buffer
 * @param {string} outputFormat - Output format (jpeg, webp, png)
 * @returns {Buffer} - Processed image buffer
 */
const processCardImage = async (inputBuffer, outputFormat = 'jpeg') => {
  try {
    const { width, height } = IMAGE_DIMENSIONS.card;
    const quality = QUALITY_SETTINGS.card[outputFormat] || 85;

    let sharpInstance = sharp(inputBuffer)
      .resize(width, height, {
        fit: 'cover', // Crop to fill exact dimensions
        position: 'center' // Center the crop
      });

    // Apply format-specific processing
    switch (outputFormat.toLowerCase()) {
      case 'jpeg':
      case 'jpg':
        return await sharpInstance
          .jpeg({ 
            quality,
            progressive: true,
            mozjpeg: true // Better compression
          })
          .toBuffer();
      
      case 'webp':
        return await sharpInstance
          .webp({ 
            quality,
            effort: 6 // Higher effort for better compression
          })
          .toBuffer();
      
      case 'png':
        return await sharpInstance
          .png({ 
            quality,
            compressionLevel: 9,
            progressive: true
          })
          .toBuffer();
      
      default:
        // Default to JPEG
        return await sharpInstance
          .jpeg({ quality: 85, progressive: true })
          .toBuffer();
    }
  } catch (error) {
    console.error('Error processing card image:', error);
    throw new Error(`Failed to process card image: ${error.message}`);
  }
};

/**
 * Process cover image for game
 * @param {Buffer} inputBuffer - Original image buffer
 * @param {string} outputFormat - Output format
 * @returns {Buffer} - Processed image buffer
 */
const processCoverImage = async (inputBuffer, outputFormat = 'jpeg') => {
  try {
    const { width, height } = IMAGE_DIMENSIONS.cover;
    const quality = QUALITY_SETTINGS.cover[outputFormat] || 90;

    let sharpInstance = sharp(inputBuffer)
      .resize(width, height, {
        fit: 'cover',
        position: 'center'
      });

    switch (outputFormat.toLowerCase()) {
      case 'jpeg':
      case 'jpg':
        return await sharpInstance
          .jpeg({ quality, progressive: true })
          .toBuffer();
      
      case 'webp':
        return await sharpInstance
          .webp({ quality, effort: 6 })
          .toBuffer();
      
      default:
        return await sharpInstance
          .jpeg({ quality: 90, progressive: true })
          .toBuffer();
    }
  } catch (error) {
    console.error('Error processing cover image:', error);
    throw new Error(`Failed to process cover image: ${error.message}`);
  }
};

/**
 * Generate multiple optimized versions of an image
 * @param {Buffer} inputBuffer - Original image buffer
 * @param {string} imageType - Type of image (card, cover, etc.)
 * @returns {Object} - Object containing different format buffers
 */
const generateOptimizedVersions = async (inputBuffer, imageType = 'card') => {
  try {
    const results = {};
    
    // Generate JPEG version (primary)
    if (imageType === 'card') {
      results.jpeg = await processCardImage(inputBuffer, 'jpeg');
      results.webp = await processCardImage(inputBuffer, 'webp');
    } else if (imageType === 'cover') {
      results.jpeg = await processCoverImage(inputBuffer, 'jpeg');
      results.webp = await processCoverImage(inputBuffer, 'webp');
    }
    
    return results;
  } catch (error) {
    console.error('Error generating optimized versions:', error);
    throw error;
  }
};

/**
 * Get image metadata
 * @param {Buffer} inputBuffer - Image buffer
 * @returns {Object} - Image metadata
 */
const getImageMetadata = async (inputBuffer) => {
  try {
    const metadata = await sharp(inputBuffer).metadata();
    return {
      width: metadata.width,
      height: metadata.height,
      format: metadata.format,
      size: metadata.size,
      hasAlpha: metadata.hasAlpha,
      channels: metadata.channels
    };
  } catch (error) {
    console.error('Error getting image metadata:', error);
    throw error;
  }
};

/**
 * Validate image dimensions and format
 * @param {Buffer} inputBuffer - Image buffer
 * @param {string} imageType - Expected image type
 * @returns {Object} - Validation result
 */
const validateImage = async (inputBuffer, imageType = 'card') => {
  try {
    const metadata = await getImageMetadata(inputBuffer);
    const minDimensions = IMAGE_DIMENSIONS[imageType];
    
    const validation = {
      isValid: true,
      errors: [],
      metadata,
      recommendations: []
    };
    
    // Check minimum dimensions
    if (metadata.width < minDimensions.width || metadata.height < minDimensions.height) {
      validation.isValid = false;
      validation.errors.push(
        `Image too small. Minimum size: ${minDimensions.width}x${minDimensions.height}, got: ${metadata.width}x${metadata.height}`
      );
    }
    
    // Check format
    const allowedFormats = ['jpeg', 'jpg', 'png', 'webp'];
    if (!allowedFormats.includes(metadata.format)) {
      validation.isValid = false;
      validation.errors.push(`Unsupported format: ${metadata.format}. Allowed: ${allowedFormats.join(', ')}`);
    }
    
    // Add recommendations
    const aspectRatio = metadata.width / metadata.height;
    const targetAspectRatio = minDimensions.width / minDimensions.height;
    
    if (Math.abs(aspectRatio - targetAspectRatio) > 0.1) {
      validation.recommendations.push(
        `Consider using images with ${targetAspectRatio.toFixed(2)}:1 aspect ratio for best results`
      );
    }
    
    return validation;
  } catch (error) {
    return {
      isValid: false,
      errors: [`Failed to validate image: ${error.message}`],
      metadata: null,
      recommendations: []
    };
  }
};

module.exports = {
  processCardImage,
  processCoverImage,
  generateOptimizedVersions,
  getImageMetadata,
  validateImage,
  IMAGE_DIMENSIONS,
  QUALITY_SETTINGS
};
