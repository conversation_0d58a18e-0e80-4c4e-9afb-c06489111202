const { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } = require('@aws-sdk/client-s3');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const path = require('path');

// Validate required environment variables
const validateS3Config = () => {
  const required = ['AWS_REGION', 'AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY', 'AWS_S3_BUCKET_NAME'];
  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    console.error('❌ Missing required S3 environment variables:', missing);
    return false;
  }
  
  console.log('✅ S3 environment variables are configured');
  return true;
};

// Initialize S3 client only if configuration is valid
let s3Client = null;
let BUCKET_NAME = null;

if (validateS3Config()) {
  s3Client = new S3Client({
    region: process.env.AWS_REGION,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
  });
  
  BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME;
  console.log(`✅ S3 client initialized for bucket: ${BUCKET_NAME} in region: ${process.env.AWS_REGION}`);
} else {
  console.error('❌ S3 client not initialized due to missing configuration');
}

/**
 * Upload file to S3
 * @param {Buffer} fileBuffer - File buffer
 * @param {string} fileName - Name of the file
 * @param {string} contentType - MIME type of the file
 * @param {string} folder - S3 folder path (optional)
 * @returns {Promise<string>} - S3 object URL
 */
const uploadFileToS3 = async (fileBuffer, fileName, contentType, folder = '') => {
  try {
    if (!s3Client) {
      throw new Error('S3 client not initialized');
    }
    
    const timestamp = Date.now();
    const fileExtension = path.extname(fileName);
    const baseName = path.basename(fileName, fileExtension);
    const uniqueFileName = `${baseName}-${timestamp}${fileExtension}`;
    
    const key = folder ? `${folder}/${uniqueFileName}` : uniqueFileName;
    
    const command = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
      Body: fileBuffer,
      ContentType: contentType,
      // Note: ACL removed - bucket policy should handle public access
    });

    await s3Client.send(command);
    
    // Use main domain instead of direct S3 URLs for better security and control
    const baseUrl = process.env.UPLOADS_BASE_URL || 'https://indierepo.s3.us-east-1.amazonaws.com';
    const publicUrl = `${baseUrl}/${key}`;
    
    console.log(`✅ File uploaded successfully to S3: ${key}`);
    console.log(`📋 Public URL: ${publicUrl}`);
    
    return {
      url: publicUrl,
      key: key,
      bucket: BUCKET_NAME
    };
  } catch (error) {
    console.error('❌ Error uploading file to S3:', error);
    throw new Error(`S3 upload failed: ${error.message}`);
  }
};

/**
 * Upload game file to S3 with organized folder structure
 * @param {Buffer} fileBuffer - File buffer
 * @param {string} fileName - Original file name
 * @param {string} contentType - MIME type
 * @param {number} gameId - Game ID for folder organization
 * @param {string} fileType - Type of file (game, cover, card, gif, screenshot)
 * @returns {Promise<Object>} - Upload result with URL and metadata
 */
const uploadGameFileToS3 = async (fileBuffer, fileName, contentType, gameId, fileType) => {
  try {
    if (!s3Client) {
      throw new Error('S3 client not initialized');
    }
    
    let folder;
    let processedFileName = fileName;
    
    // Organize files by type
    switch (fileType) {
      case 'cover':
        folder = `games/${gameId}/images`;
        processedFileName = `cover${path.extname(fileName)}`;
        break;
      case 'card':
        folder = `games/${gameId}/images`;
        processedFileName = `card${path.extname(fileName)}`;
        break;
      case 'gif':
        folder = `games/${gameId}/images`;
        processedFileName = 'hover.gif';
        break;
      case 'screenshot':
        folder = `games/${gameId}/screenshots`;
        break;
      case 'game':
      default:
        folder = `games/${gameId}/files`;
        break;
    }
    
    return await uploadFileToS3(fileBuffer, processedFileName, contentType, folder);
  } catch (error) {
    console.error(`❌ Error uploading ${fileType} file for game ${gameId}:`, error);
    throw error;
  }
};

/**
 * Delete file from S3
 * @param {string} key - S3 object key
 * @returns {Promise<void>}
 */
const deleteFileFromS3 = async (key) => {
  try {
    if (!s3Client) {
      throw new Error('S3 client not initialized');
    }
    
    const command = new DeleteObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    });

    await s3Client.send(command);
    console.log(`✅ File deleted successfully from S3: ${key}`);
  } catch (error) {
    console.error('❌ Error deleting file from S3:', error);
    throw new Error(`S3 deletion failed: ${error.message}`);
  }
};

/**
 * Generate presigned URL for secure file access
 * @param {string} key - S3 object key
 * @param {number} expiresIn - URL expiration time in seconds (default: 1 hour)
 * @returns {Promise<string>} - Presigned URL
 */
const generatePresignedUrl = async (key, expiresIn = 3600) => {
  try {
    if (!s3Client) {
      throw new Error('S3 client not initialized');
    }
    
    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    });

    const presignedUrl = await getSignedUrl(s3Client, command, { expiresIn });
    return presignedUrl;
  } catch (error) {
    console.error('❌ Error generating presigned URL:', error);
    throw new Error(`Presigned URL generation failed: ${error.message}`);
  }
};

/**
 * Check if S3 is properly configured
 * @returns {Promise<boolean>}
 */
const checkS3Configuration = async () => {
  try {
    console.log('🔍 S3 Configuration Check Details:');
    console.log('- Bucket Name:', BUCKET_NAME);
    console.log('- Region:', process.env.AWS_REGION);
    console.log('- S3 Client Initialized:', !!s3Client);
    
    if (!s3Client) {
      console.error('❌ S3 client not initialized - check environment variables');
      return false;
    }
    
    // Try to upload a test file to check credentials and permissions
    const testKey = `test-connection-${Date.now()}.txt`;
    console.log('🧪 Testing S3 upload with key:', testKey);
    
    const command = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: testKey,
      Body: Buffer.from('S3 connection test'),
      ContentType: 'text/plain',
    });
    
    await s3Client.send(command);
    console.log('✅ S3 upload test successful');
    
    // Clean up test file
    await deleteFileFromS3(testKey);
    console.log('✅ S3 delete test successful');
    
    console.log('✅ S3 connection successful');
    return true;
  } catch (error) {
    console.error('❌ S3 connection failed:', error.message);
    console.error('Error details:', {
      name: error.name,
      code: error.code,
      statusCode: error.$metadata?.httpStatusCode,
      bucket: BUCKET_NAME,
      region: process.env.AWS_REGION
    });
    
    // Provide more specific error guidance
    if (error.message.includes('bucket does not allow ACLs')) {
      console.error('💡 Tip: Your S3 bucket has ACLs disabled. This has been fixed in the code.');
    } else if (error.message.includes('Access Denied')) {
      console.error('💡 Tip: Check your AWS credentials and bucket permissions.');
    } else if (error.message.includes('NoSuchBucket')) {
      console.error('💡 Tip: Check if your S3 bucket name is correct in environment variables.');
      console.error('💡 Expected bucket name: indierepo');
    } else if (error.message.includes('InvalidAccessKeyId')) {
      console.error('💡 Tip: AWS Access Key ID is invalid or not set.');
    } else if (error.message.includes('SignatureDoesNotMatch')) {
      console.error('💡 Tip: AWS Secret Access Key is invalid or not set.');
    }
    
    return false;
  }
};

module.exports = {
  uploadFileToS3,
  uploadGameFileToS3,
  deleteFileFromS3,
  generatePresignedUrl,
  checkS3Configuration,
  s3Client,
  BUCKET_NAME
}; 