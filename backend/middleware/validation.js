const Joi = require('joi');
const logger = require('../config/logger');

/**
 * Generic validation middleware factory
 */
const validate = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error } = schema.validate(req[property]);
    
    if (error) {
      const { details } = error;
      const message = details.map(i => i.message).join(',');
      
      logger.warn(`Validation error: ${message}`);
      return res.status(400).json({ 
        message: 'Validation error',
        errors: details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
    }
    
    next();
  };
};

/**
 * Common validation schemas
 */
const schemas = {
  // User registration validation
  register: Joi.object({
    username: Joi.string().alphanum().min(3).max(30).required(),
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required()
  }),

  // User login validation
  login: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required()
  }),

  // Profile update validation
  profileUpdate: Joi.object({
    username: Joi.string().alphanum().min(3).max(30),
    bio: Joi.string().max(500).allow(''),
    email: Joi.string().email()
  }),

  // Game creation validation
  gameCreate: Joi.object({
    title: Joi.string().min(1).max(100).required(),
    description: Joi.string().max(1000).required(),
    category: Joi.string().valid('action', 'adventure', 'puzzle', 'strategy', 'arcade', 'other').required(),
    tags: Joi.array().items(Joi.string().max(20)).max(10),
    isPublic: Joi.boolean().default(true)
  }),

  // Review validation
  reviewCreate: Joi.object({
    rating: Joi.number().integer().min(1).max(5).required(),
    comment: Joi.string().max(500).allow('')
  }),

  // Query parameter validation
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    sort: Joi.string().valid('createdAt', 'title', 'rating').default('createdAt'),
    order: Joi.string().valid('asc', 'desc').default('desc')
  })
};

module.exports = {
  validate,
  schemas
}; 