const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Set up storage for avatar uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const userId = req.user.id;
    const uploadDir = path.join(__dirname, '../../uploads/avatars', userId.toString());
    
    // Create the directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Create a unique filename with timestamp
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const fileExt = path.extname(file.originalname);
    cb(null, 'avatar-' + uniqueSuffix + fileExt);
  }
});

// Filter to only accept image files
const fileFilter = (req, file, cb) => {
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Not an image! Please upload only images.'), false);
  }
};

// Configure multer
const avatarUpload = multer({
  storage: storage,
  limits: {
    fileSize: 1024 * 1024 // 1MB max file size
  },
  fileFilter: fileFilter
});

module.exports = avatarUpload;
