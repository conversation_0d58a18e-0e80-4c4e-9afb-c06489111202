const multer = require('multer');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Define allowed file types
const gameFileTypes = ['.zip', '.rar', '.7z'];
const imageFileTypes = ['.jpg', '.jpeg', '.png', '.gif'];

// Create storage engine
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Create a single temporary destination for all files in this request
    // This is to make it easier to clean up if needed
    const tempDir = path.join(__dirname, `../../uploads/temp/${uuidv4()}`);
    
    // Create directory if it doesn't exist
    fs.mkdirSync(tempDir, { recursive: true });
    
    
    cb(null, tempDir);
  },
  filename: function (req, file, cb) {
    // Create a unique filename to avoid overwriting
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

// File filter function
const fileFilter = (req, file, cb) => {
  const ext = path.extname(file.originalname).toLowerCase();
  
  // Check if the file type is allowed based on fieldname
  if (file.fieldname === 'gameFile') {
    if (gameFileTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Only .zip, .rar and .7z files are allowed for games'), false);
    }
  } else if (file.fieldname === 'gifAnimation') {
    if (ext === '.gif') {
      cb(null, true);
    } else {
      cb(new Error('Only .gif files are allowed for animations'), false);
    }
  } else {
    // coverImage, cardImage, or screenshots
    if (imageFileTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Only .jpg, .jpeg, and .png files are allowed for images'), false);
    }
  }
};

// Configure multer with size limits
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 500 * 1024 * 1024, // 500MB max file size for game files
    files: 10 // max 10 files total
  }
});

module.exports = upload;
