const { OAuth2Client } = require('google-auth-library');
const axios = require('axios');
const jwt = require('jsonwebtoken');
const db = require('../config/database');
const bcrypt = require('bcryptjs');
const authService = require('../services/auth.service');
const { getRandomDefaultAvatar } = require('../constants/defaultAvatars');

// Initialize Google OAuth client
const googleClient = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

// Generate JWT token
const generateToken = (user) => {
  return jwt.sign(
    { 
      userId: user.id, 
      email: user.email, 
      role: user.role 
    },
    process.env.JWT_SECRET,
    { expiresIn: '7d' }
  );
};

// Helper function to set authentication cookie
const setAuthCookie = (res, token, rememberMe = false) => {
  // Cookie configuration
  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: rememberMe ? 7 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000, // 7 days or 1 day
    path: '/'
  };

  // Get the hostname from the request
  const hostname = res.req.get('host') ? res.req.get('host').split(':')[0] : 'localhost';

  // Improved domain detection logic
  if (hostname !== 'localhost' && hostname !== '127.0.0.1') {
    // For development with custom domains in /etc/hosts (like indierepo.com)
    if (hostname === 'indierepo.com' || hostname.endsWith('.indierepo.com')) {
      // Use .indierepo.com for all indierepo domains and subdomains
      cookieOptions.domain = '.indierepo.com';
    }
    // For other domains, try to extract the root domain
    else {
      // Extract the root domain (e.g., "example.com" from "sub.example.com")
      const domainParts = hostname.split('.');
      if (domainParts.length >= 2) {
        // For proper subdomain support, we need the root domain with a leading dot
        cookieOptions.domain = `.${domainParts.slice(-2).join('.')}`;
      }
    }
  }

  // Set the cookie
  res.cookie('token', token, cookieOptions);
};

// Export token generator for reuse by other modules
exports.generateToken = generateToken;
exports.setAuthCookie = setAuthCookie;

// Register new user handler - using Knex queries
exports.register = async (req, res) => {
  try {
    const { username, email, password } = req.body;

    // Validation
    if (!username || !email || !password) {
      return res.status(400).json({ 
        message: 'All fields are required' 
      });
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ 
        message: 'Invalid email format' 
      });
    }

    // Password validation
    if (password.length < 6) {
      return res.status(400).json({ 
        message: 'Password must be at least 6 characters long' 
      });
    }

    // Check if user already exists
    const existingUser = await db('users')
      .where('email', email.toLowerCase())
      .orWhere('username', username.toLowerCase())
      .first();

    if (existingUser) {
      return res.status(400).json({ 
        message: existingUser.email === email.toLowerCase() 
          ? 'Email already registered' 
          : 'Username already taken'
      });
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Get a random default avatar
    const defaultAvatar = getRandomDefaultAvatar();

    // Create new user
    const [userResult] = await db('users').insert({
      username: username.toLowerCase(),
      email: email.toLowerCase(),
      password: hashedPassword,
      profile_image: defaultAvatar.url,
      provider: 'local'
    }).returning('id');

    // Extract the actual ID from the result object
    const userId = userResult.id || userResult;

    // Get the created user
    const user = await db('users')
      .select('id', 'username', 'email', 'role')
      .where('id', userId)
      .first();

    // Generate token
    const token = generateToken(user);
    
    // Set the authentication cookie
    setAuthCookie(res, token);

    // Send response without token in body
    res.status(201).json({
      message: 'Registration successful',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ 
      message: 'Error during registration',
      error: error.message 
    });
  }
};

// Google login handler
exports.googleLogin = async (req, res) => {
  try {
    const { token, tokenType } = req.body;

    let userInfo;
    
    // Get user info based on token type
    if (tokenType === 'id_token') {
      // Verify ID token
      const ticket = await googleClient.verifyIdToken({
        idToken: token,
        audience: process.env.GOOGLE_CLIENT_ID
      });
      userInfo = ticket.getPayload();
    } else {
      // Use access token to get user info from Google API
      const response = await axios.get('https://www.googleapis.com/oauth2/v3/userinfo', {
        headers: { Authorization: `Bearer ${token}` }
      });
      userInfo = response.data;
    }

    if (!userInfo || !userInfo.email) {
      return res.status(400).json({ message: 'Invalid Google token' });
    }

    // Find user by email
    let user = await db('users').where('email', userInfo.email).first();
    
    if (!user) {
      // Generate a unique username
      const username = `${userInfo.email.split('@')[0]}${Math.floor(Math.random() * 1000)}`;
      
      // Use Google's profile picture if available, otherwise use default avatar
      const profileImage = userInfo.picture || getRandomDefaultAvatar().url;
      
      // Create new user with Google info
      const [userResult] = await db('users').insert({
        email: userInfo.email,
        username: username.toLowerCase(),
        display_name: userInfo.name,
        profile_image: profileImage,
        google_id: userInfo.sub,
        provider: 'google',
        is_verified: true,
        is_google_user: true
      }).returning('id');

      // Extract the actual ID from the result object
      const userId = userResult.id || userResult;
      user = await db('users').where('id', userId).first();
    } else {
      // Update existing user with Google ID if not set
      if (!user.google_id) {
        const updateData = {
          google_id: userInfo.sub,
          is_google_user: true,
          provider: 'google',
          is_verified: true
        };

        // Only set profile image if user doesn't have one and Google provides one
        if (!user.profile_image && userInfo.picture) {
          updateData.profile_image = userInfo.picture;
        } else if (!user.profile_image) {
          // If no Google picture and no existing avatar, assign default
          updateData.profile_image = getRandomDefaultAvatar().url;
        }

        await db('users')
          .where('id', user.id)
          .update(updateData);
        
        // Get updated user
        user = await db('users').where('id', user.id).first();
      }
    }

    // Generate token and set cookie
    const authToken = generateToken(user);
    setAuthCookie(res, authToken, true);

    // Send response with user data
    res.json({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        displayName: user.display_name,
        profileImage: user.profile_image,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Google login error:', error);
    res.status(500).json({ 
      message: 'Google authentication failed',
      error: error.message 
    });
  }
};

// Discord login handler
exports.discordLogin = async (req, res) => {
  try {
    const { code, redirectUri } = req.body;
    
    if (!code) {
      return res.status(400).json({ message: 'No authorization code provided' });
    }
    
    // Use the auth service method
    const authService = require('../services/auth.service');
    const result = await authService.discordLogin(code, redirectUri);
    
    if (result.success) {
      // Set cookie for the authenticated user
      setAuthCookie(res, result.token, true);
      
      res.json({
        message: 'Discord authentication successful',
        user: result.user
      });
    } else {
      res.status(400).json({ 
        message: result.message || 'Discord authentication failed' 
      });
    }
  } catch (error) {
    console.error('Discord login error:', error);
    res.status(500).json({ 
      message: 'Discord authentication failed',
      error: error.message 
    });
  }
};

// Discord callback handler  
exports.discordCallback = async (req, res) => {
  try {
    const { code, redirectUri } = req.body;
    
    if (!code) {
      return res.status(400).json({ message: 'No authorization code provided' });
    }
    
    const result = await authService.discordLogin(code, redirectUri);
    
    if (result.success) {
      // Set cookie for the authenticated user
      setAuthCookie(res, result.token, true);
      
      res.json({
        message: 'Discord login successful',
        user: result.user
      });
    } else {
      res.status(400).json({ message: result.message });
    }
  } catch (error) {
    console.error('Discord login error:', error);
    res.status(500).json({ message: 'Server error during Discord authentication' });
  }
};

// Login handler - using Knex queries
exports.login = async (req, res) => {
  try {
    const { email, password, rememberMe } = req.body;

    if (!email || !password) {
      return res.status(400).json({ 
        message: 'Email and password are required' 
      });
    }

    // Find user by email
    const user = await db('users').where('email', email.toLowerCase()).first();

    if (!user) {
      return res.status(401).json({ 
        message: 'Invalid email or password' 
      });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ 
        message: 'Invalid email or password' 
      });
    }

    // Generate token
    const token = generateToken(user);
    
    // Set the authentication cookie
    setAuthCookie(res, token, rememberMe);

    // Send response without password
    res.json({
      message: 'Login successful',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        displayName: user.display_name,
        profileImage: user.profile_image,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ 
      message: 'Error during login',
      error: error.message 
    });
  }
};

// Get current user handler
exports.getCurrentUser = async (req, res) => {
  try {
    // Get user from database (req.user only has id, email, role from JWT)
    const user = await db('users')
      .select('id', 'username', 'email', 'role', 'display_name', 'profile_image')
      .where('id', req.user.userId)
      .first();

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        displayName: user.display_name,
        profileImage: user.profile_image
      }
    });
  } catch (error) {
    console.error('Get current user error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Logout handler
exports.logout = (req, res) => {
  res.clearCookie('token', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/'
  });
  
  res.json({ message: 'Logged out successfully' });
};

// Change password handler
exports.changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.userId;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({ 
        message: 'Current password and new password are required' 
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({ 
        message: 'New password must be at least 6 characters long' 
      });
    }

    // Get user from database
    const user = await db('users').where('id', userId).first();

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // For users who signed up with social login and don't have a password
    if (!user.password) {
      // Hash new password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(newPassword, salt);

      // Update user with new password
      await db('users')
        .where('id', userId)
        .update({
          password: hashedPassword,
          provider: 'local'
        });

      return res.json({ message: 'Password set successfully' });
    }

    // Verify current password
    const isValidPassword = await bcrypt.compare(currentPassword, user.password);
    if (!isValidPassword) {
      return res.status(400).json({ 
        message: 'Current password is incorrect' 
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update user with new password
    await db('users')
      .where('id', userId)
      .update({ password: hashedPassword });

    res.json({ message: 'Password changed successfully' });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({ 
      message: 'Error changing password',
      error: error.message 
    });
  }
};

// Health check endpoint
exports.health = (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'IndieRepo Auth API'
  });
};
