const db = require('../config/database');

// Get reviews for a game
exports.getGameReviews = async (req, res) => {
  try {
    const { gameId } = req.params;
    const userId = req.user ? req.user.userId : null;
    
    // Base query to get reviews with user info
    let reviews = await db('reviews as r')
      .select(
        'r.id', 'r.user_id', 'r.game_id', 'r.rating', 'r.title', 'r.comment',
        'r.likes_count', 'r.dislikes_count', 'r.comment_count', 'r.created_at', 'r.updated_at',
        'u.username as user', 'u.profile_image as avatar'
      )
      .join('users as u', 'r.user_id', 'u.id')
      .where('r.game_id', gameId)
      .orderBy('r.created_at', 'desc');

    // If user is logged in, get their reactions to these reviews
    if (userId && reviews.length > 0) {
      const reviewIds = reviews.map(review => review.id);
      
      const reactions = await db('review_reactions')
        .select('review_id', 'reaction_type')
        .where('user_id', userId)
        .whereIn('review_id', reviewIds);
      
      // Create a map for quick lookup
      const reactionMap = {};
      reactions.forEach(reaction => {
        reactionMap[reaction.review_id] = reaction.reaction_type;
      });
      
      // Add user reaction to each review
      reviews = reviews.map(review => ({
        ...review,
        userReaction: reactionMap[review.id] || 'none'
      }));
    }
    
    // Ensure numeric values for counts and format dates
    reviews = reviews.map(review => ({
      ...review,
      likesCount: parseInt(review.likes_count || 0),
      dislikesCount: parseInt(review.dislikes_count || 0),
      commentCount: parseInt(review.comment_count || 0),
      date: new Date(review.created_at).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }));
    
    res.json(reviews);
  } catch (error) {
    console.error('Error fetching game reviews:', error);
    res.status(500).json({ message: 'Server error while fetching reviews' });
  }
};

// Submit a review for a game
exports.submitReview = async (req, res) => {
  try {
    const { gameId } = req.params;
    const { title, comment, rating } = req.body;
    const userId = req.user.userId;
    
    // Check if user already reviewed this game
    const existingReview = await db('reviews')
      .where('user_id', userId)
      .where('game_id', gameId)
      .first();
    
    if (existingReview) {
      return res.status(400).json({ message: 'You already reviewed this game' });
    }
    
    // Add the new review
    const [reviewResult] = await db('reviews').insert({
      user_id: userId,
      game_id: gameId,
      title,
      comment,
      rating
    }).returning('id');

    // Extract the actual ID from the result object
    const reviewId = reviewResult.id || reviewResult;

    res.status(201).json({
      message: 'Review submitted successfully',
      reviewId
    });
  } catch (error) {
    console.error('Error submitting review:', error);
    res.status(500).json({ message: 'Server error while submitting review' });
  }
};

// Handle review reactions (likes/dislikes)
exports.handleReviewReaction = async (req, res) => {
  try {
    const { reviewId } = req.params;
    const { reactionType } = req.body;
    const userId = req.user.userId;

    if (!['like', 'dislike', 'none'].includes(reactionType)) {
      return res.status(400).json({ message: 'Invalid reaction type' });
    }

    // Use transaction for data consistency
    await db.transaction(async (trx) => {
      // Check if user has already reacted to this review
      const existingReaction = await trx('review_reactions')
        .where('user_id', userId)
        .where('review_id', reviewId)
        .first();

      if (reactionType === 'none') {
        // Remove reaction if it exists
        if (existingReaction) {
          const oldReactionType = existingReaction.reaction_type;

          // Delete the reaction
          await trx('review_reactions')
            .where('user_id', userId)
            .where('review_id', reviewId)
            .del();

          // Update counts in Reviews table
          if (oldReactionType === 'like') {
            await trx('reviews').where('id', reviewId).decrement('likes_count', 1);
          } else {
            await trx('reviews').where('id', reviewId).decrement('dislikes_count', 1);
          }
        }
      } else if (existingReaction) {
        // User already has a reaction - check if it's the same
        const oldReactionType = existingReaction.reaction_type;

        if (oldReactionType !== reactionType) {
          // Update the reaction type
          await trx('review_reactions')
            .where('user_id', userId)
            .where('review_id', reviewId)
            .update({ reaction_type: reactionType });

          // Update counts in Reviews table
          if (oldReactionType === 'like') {
            await trx('reviews')
              .where('id', reviewId)
              .decrement('likes_count', 1)
              .increment('dislikes_count', 1);
          } else {
            await trx('reviews')
              .where('id', reviewId)
              .increment('likes_count', 1)
              .decrement('dislikes_count', 1);
          }
        }
        // If same reaction, do nothing (handled by frontend)
      } else {
        // Create new reaction
        await trx('review_reactions').insert({
          user_id: userId,
          review_id: reviewId,
          reaction_type: reactionType
        });

        // Update counts in Reviews table
        if (reactionType === 'like') {
          await trx('reviews').where('id', reviewId).increment('likes_count', 1);
        } else {
          await trx('reviews').where('id', reviewId).increment('dislikes_count', 1);
        }
      }
    });

    // Get updated review counts
    const updatedReview = await db('reviews')
      .select('likes_count', 'dislikes_count')
      .where('id', reviewId)
      .first();

    res.json({
      message: 'Reaction updated successfully',
      likesCount: updatedReview.likes_count,
      dislikesCount: updatedReview.dislikes_count
    });
  } catch (error) {
    console.error('Error handling review reaction:', error);
    res.status(500).json({ message: 'Server error while handling reaction' });
  }
};

// Get comments for a review
exports.getReviewComments = async (req, res) => {
  try {
    const { reviewId } = req.params;
    
    const comments = await db('review_comments as c')
      .select(
        'c.id', 'c.content', 'c.created_at',
        'u.username', 'u.profile_image as avatar'
      )
      .join('users as u', 'c.user_id', 'u.id')
      .where('c.review_id', reviewId)
      .orderBy('c.created_at', 'desc');

    res.json(comments);
  } catch (error) {
    console.error('Error fetching review comments:', error);
    res.status(500).json({ message: 'Server error while fetching comments' });
  }
};

// Add a comment to a review
exports.addReviewComment = async (req, res) => {
  try {
    const { reviewId } = req.params;
    const { content } = req.body;
    const userId = req.user.userId;

    if (!content || content.trim().length === 0) {
      return res.status(400).json({ message: 'Comment content is required' });
    }

    // Check if review exists
    const review = await db('reviews')
      .select('id')
      .where('id', reviewId)
      .first();

    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    // Add the comment
    const [commentResult] = await db('review_comments').insert({
      review_id: reviewId,
      user_id: userId,
      content: content.trim()
    }).returning('id');

    // Extract the actual ID from the result object
    const commentId = commentResult.id || commentResult;

    // Get the new comment with user info
    const newComment = await db('review_comments as c')
      .select(
        'c.id', 'c.content', 'c.created_at',
        'u.username', 'u.profile_image as avatar'
      )
      .join('users as u', 'c.user_id', 'u.id')
      .where('c.id', commentId)
      .first();

    // Update the comment count in the Reviews table
    await db('reviews')
      .where('id', reviewId)
      .update({
        comment_count: db.raw('(SELECT COUNT(*) FROM review_comments WHERE review_id = ?)', [reviewId])
      });

    res.status(201).json({
      message: 'Comment added successfully',
      comment: newComment
    });
  } catch (error) {
    console.error('Error adding review comment:', error);
    res.status(500).json({ message: 'Server error while adding comment' });
  }
};

// Delete a review (only by review author or admin)
exports.deleteReview = async (req, res) => {
  try {
    const { reviewId } = req.params;
    const userId = req.user.userId;
    const userRole = req.user.role;

    // Get the review to check ownership
    const review = await db('reviews')
      .select('user_id')
      .where('id', reviewId)
      .first();

    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    // Check if user owns the review or is admin
    if (review.user_id !== userId && userRole !== 'admin') {
      return res.status(403).json({ message: 'Not authorized to delete this review' });
    }

    // Use transaction to delete review and related data
    await db.transaction(async (trx) => {
      // Delete all reactions for this review
      await trx('review_reactions').where('review_id', reviewId).del();

      // Delete all comments for this review
      await trx('review_comments').where('review_id', reviewId).del();

      // Delete the review itself
      await trx('reviews').where('id', reviewId).del();
    });

    res.json({ message: 'Review deleted successfully' });
  } catch (error) {
    console.error('Error deleting review:', error);
    res.status(500).json({ message: 'Server error while deleting review' });
  }
};
