const db = require('../config/database');
const bcrypt = require('bcryptjs');
require('dotenv').config();

/**
 * User model operations using Knex
 */
const User = {
  // Find user by ID
  async findById(id) {
    try {
      const user = await db('users').where('id', id).first();
      return user || null;
    } catch (error) {
      throw error;
    }
  },

  // Find user by email
  async findByEmail(email) {
    try {
      const user = await db('users').where('email', email.toLowerCase()).first();
      return user || null;
    } catch (error) {
      throw error;
    }
  },

  // Find user by username
  async findByUsername(username) {
    try {
      const user = await db('users').where('username', username.toLowerCase()).first();
      return user || null;
    } catch (error) {
      throw error;
    }
  },

  // Find user by Google ID
  async findByGoogleId(googleId) {
    try {
      const user = await db('users').where('google_id', googleId).first();
      return user || null;
    } catch (error) {
      throw error;
    }
  },

  // Find user by Discord ID
  async findByDiscordId(discordId) {
    try {
      const user = await db('users').where('discord_id', discordId).first();
      return user || null;
    } catch (error) {
      throw error;
    }
  },

  // Create new user
  async create(userData) {
    try {
      const [result] = await db('users').insert({
        username: userData.username?.toLowerCase(),
        email: userData.email?.toLowerCase(),
        password: userData.password,
        google_id: userData.googleId,
        discord_id: userData.discordId,
        profile_image: userData.profileImage,
        display_name: userData.displayName,
        is_google_user: userData.isGoogleUser || false,
        is_verified: userData.isVerified || false,
        provider: userData.provider || 'local',
        role: userData.role || 'user',
        credits: userData.credits || 0,
        referral_code: userData.referralCode
      }).returning('id');

      // Extract the actual ID from the result object
      const id = result.id || result;
      return await this.findById(id);
    } catch (error) {
      throw error;
    }
  },

  // Update user
  async update(id, updateData) {
    try {
      const updateFields = {};
      
      // Map camelCase to snake_case
      if (updateData.username) updateFields.username = updateData.username.toLowerCase();
      if (updateData.email) updateFields.email = updateData.email.toLowerCase();
      if (updateData.password) updateFields.password = updateData.password;
      if (updateData.googleId !== undefined) updateFields.google_id = updateData.googleId;
      if (updateData.discordId !== undefined) updateFields.discord_id = updateData.discordId;
      if (updateData.profileImage !== undefined) updateFields.profile_image = updateData.profileImage;
      if (updateData.displayName !== undefined) updateFields.display_name = updateData.displayName;
      if (updateData.isGoogleUser !== undefined) updateFields.is_google_user = updateData.isGoogleUser;
      if (updateData.isVerified !== undefined) updateFields.is_verified = updateData.isVerified;
      if (updateData.provider) updateFields.provider = updateData.provider;
      if (updateData.role) updateFields.role = updateData.role;
      if (updateData.credits !== undefined) updateFields.credits = updateData.credits;
      if (updateData.referralCode !== undefined) updateFields.referral_code = updateData.referralCode;

      updateFields.updated_at = new Date();

      await db('users').where('id', id).update(updateFields);
      return await this.findById(id);
    } catch (error) {
      throw error;
    }
  },

  // Compare password
  async comparePassword(plainPassword, hashedPassword) {
    if (!hashedPassword) return false;
    return bcrypt.compare(plainPassword, hashedPassword);
  },

  // Hash password
  async hashPassword(password) {
    const salt = await bcrypt.genSalt(10);
    return bcrypt.hash(password, salt);
  }
};

/**
 * Game model operations using Knex
 */
const Game = {
  // Find all games with optional filters
  async findAll(filters = {}, options = {}) {
    try {
      let query = db('games as g')
        .select('g.*', 'u.username as publisher_name')
        .join('users as u', 'g.user_id', 'u.id');

      // Apply filters
      if (filters.genre) {
        query = query.where('g.genre', filters.genre);
      }
      if (filters.tags) {
        query = query.where('g.tags', 'ilike', `%${filters.tags}%`);
      }
      if (filters.search) {
        query = query.where(function() {
          this.where('g.title', 'ilike', `%${filters.search}%`)
              .orWhere('g.description', 'ilike', `%${filters.search}%`);
        });
      }
      if (filters.userId) {
        query = query.where('g.user_id', filters.userId);
      }

      // Apply sorting
      const sortBy = options.sort || 'newest';
      switch (sortBy) {
        case 'newest':
          query = query.orderBy('g.created_at', 'desc');
          break;
        case 'oldest':
          query = query.orderBy('g.created_at', 'asc');
          break;
        case 'popular':
          query = query.orderBy('g.view_count', 'desc');
          break;
        case 'downloads':
          query = query.orderBy('g.download_count', 'desc');
          break;
        case 'alphabetical':
          query = query.orderBy('g.title', 'asc');
          break;
        default:
          query = query.orderBy('g.created_at', 'desc');
      }

      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit);
        if (options.offset) {
          query = query.offset(options.offset);
        }
      }

      return await query;
    } catch (error) {
      throw error;
    }
  },

  // Find game by ID
  async findById(id) {
    try {
      const game = await db('games as g')
        .select('g.*', 'u.username as publisher_name', 'u.display_name as publisher_display_name')
        .join('users as u', 'g.user_id', 'u.id')
        .where('g.id', id)
        .first();
      
      return game || null;
    } catch (error) {
      throw error;
    }
  },

  // Create new game
  async create(gameData) {
    try {
      const [result] = await db('games').insert({
        user_id: gameData.userId,
        title: gameData.title,
        description: gameData.description,
        genre: gameData.genre,
        tags: gameData.tags,
        price: gameData.price || 0.00,
        is_free: gameData.isFree !== false,
        status: gameData.status || 'published',
        file_path: gameData.filePath,
        file_size: gameData.fileSize,
        version: gameData.version || '1.0.0',
        changelog: gameData.changelog,
        platform: gameData.platform || 'web'
      }).returning('id');

      // Extract the actual ID from the result object
      const id = result.id || result;
      return await this.findById(id);
    } catch (error) {
      throw error;
    }
  },

  // Update game
  async update(id, updateData) {
    try {
      const updateFields = {};
      
      if (updateData.title) updateFields.title = updateData.title;
      if (updateData.description !== undefined) updateFields.description = updateData.description;
      if (updateData.genre) updateFields.genre = updateData.genre;
      if (updateData.tags !== undefined) updateFields.tags = updateData.tags;
      if (updateData.price !== undefined) updateFields.price = updateData.price;
      if (updateData.isFree !== undefined) updateFields.is_free = updateData.isFree;
      if (updateData.status) updateFields.status = updateData.status;
      if (updateData.filePath) updateFields.file_path = updateData.filePath;
      if (updateData.fileSize !== undefined) updateFields.file_size = updateData.fileSize;
      if (updateData.version) updateFields.version = updateData.version;
      if (updateData.changelog !== undefined) updateFields.changelog = updateData.changelog;
      if (updateData.platform) updateFields.platform = updateData.platform;
      if (updateData.viewCount !== undefined) updateFields.view_count = updateData.viewCount;
      if (updateData.downloadCount !== undefined) updateFields.download_count = updateData.downloadCount;

      updateFields.updated_at = new Date();

      await db('games').where('id', id).update(updateFields);
      return await this.findById(id);
    } catch (error) {
      throw error;
    }
  },

  // Delete game
  async delete(id) {
    try {
      return await db('games').where('id', id).del();
    } catch (error) {
      throw error;
    }
  },

  // Increment view count
  async incrementViewCount(id) {
    try {
      return await db('games').where('id', id).increment('view_count', 1);
    } catch (error) {
      throw error;
    }
  },

  // Increment download count
  async incrementDownloadCount(id) {
    try {
      return await db('games').where('id', id).increment('download_count', 1);
    } catch (error) {
      throw error;
    }
  }
};

// Test the connection
async function testConnection() {
  try {
    await db.raw('SELECT 1');
    console.log('Knex database connection established successfully.');
    return true;
  } catch (error) {
    console.error('Unable to connect to the database with Knex:', error);
    return false;
  }
}

module.exports = {
  db,
  User,
  Game,
  testConnection
};
