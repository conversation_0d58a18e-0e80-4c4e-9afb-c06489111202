const bcrypt = require('bcryptjs');
const db = require('../config/database');

/**
 * User model functions using Knex
 * This module provides a compatibility layer with the existing Sequelize-like API
 */

const User = {
  // Create a new user
  async create(userData) {
    try {
      const [result] = await db('users').insert({
        username: userData.username?.toLowerCase(),
        email: userData.email?.toLowerCase(),
        password: userData.password,
        google_id: userData.googleId,
        discord_id: userData.discordId,
        profile_image: userData.profileImage,
        display_name: userData.displayName,
        is_google_user: userData.isGoogleUser || false,
        is_verified: userData.isVerified || false,
        provider: userData.provider || 'local',
        role: userData.role || 'user',
        credits: userData.credits || 0,
        referral_code: userData.referralCode
      }).returning('id');

      // Extract the actual ID from the result object
      const id = result.id || result;
      return await this.findByPk(id);
    } catch (error) {
      throw error;
    }
  },

  // Find user by primary key (ID)
  async findByPk(id) {
    try {
      const user = await db('users').where('id', id).first();
      if (user) {
        // Add the comparePassword method to the user object
        user.comparePassword = async function(candidatePassword) {
          if (!this.password) return false;
          return bcrypt.compare(candidatePassword, this.password);
        };
      }
      return user || null;
    } catch (error) {
      throw error;
    }
  },

  // Find user by email or username (for Sequelize compatibility)
  async findOne(options) {
    try {
      let query = db('users');
      
      if (options.where) {
        // Handle OR conditions for email/username
        if (options.where.email && options.where.username) {
          query = query.where(function() {
            this.where('email', options.where.email)
                .orWhere('username', options.where.username);
          });
        } else if (options.where.email) {
          query = query.where('email', options.where.email);
        } else if (options.where.username) {
          query = query.where('username', options.where.username);
        } else if (options.where.google_id) {
          query = query.where('google_id', options.where.google_id);
        } else if (options.where.discord_id) {
          query = query.where('discord_id', options.where.discord_id);
        } else if (options.where.id) {
          query = query.where('id', options.where.id);
        } else {
          // Apply all other where conditions
          Object.keys(options.where).forEach(key => {
            query = query.where(key, options.where[key]);
          });
        }
      }

      const user = await query.first();
      
      if (user) {
        // Add the comparePassword method to the user object
        user.comparePassword = async function(candidatePassword) {
          if (!this.password) return false;
          return bcrypt.compare(candidatePassword, this.password);
        };
      }
      
      return user || null;
    } catch (error) {
      throw error;
    }
  },

  // Update user
  async update(id, updateData) {
    try {
      const updateFields = {};
      
      // Map camelCase to snake_case for database fields
      if (updateData.username) updateFields.username = updateData.username.toLowerCase();
      if (updateData.email) updateFields.email = updateData.email.toLowerCase();
      if (updateData.password) updateFields.password = updateData.password;
      if (updateData.googleId !== undefined) updateFields.google_id = updateData.googleId;
      if (updateData.discordId !== undefined) updateFields.discord_id = updateData.discordId;
      if (updateData.profileImage !== undefined) updateFields.profile_image = updateData.profileImage;
      if (updateData.displayName !== undefined) updateFields.display_name = updateData.displayName;
      if (updateData.isGoogleUser !== undefined) updateFields.is_google_user = updateData.isGoogleUser;
      if (updateData.isVerified !== undefined) updateFields.is_verified = updateData.isVerified;
      if (updateData.provider) updateFields.provider = updateData.provider;
      if (updateData.role) updateFields.role = updateData.role;
      if (updateData.credits !== undefined) updateFields.credits = updateData.credits;
      if (updateData.referralCode !== undefined) updateFields.referral_code = updateData.referralCode;

      updateFields.updated_at = new Date();

      await db('users').where('id', id).update(updateFields);
      return await this.findByPk(id);
    } catch (error) {
      throw error;
    }
  }
};

// For backwards compatibility, export a function that returns the User object
module.exports = () => User;
