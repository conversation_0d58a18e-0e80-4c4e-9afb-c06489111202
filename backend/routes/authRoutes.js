const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { authenticateToken } = require('../middleware/authMiddleware');

// User registration
router.post('/register', authController.register);

// Google authentication
router.post('/google', authController.googleLogin);

// Discord authentication
router.post('/discord', authController.discordLogin);
// Add the missing Discord callback endpoint
router.post('/discord/callback', authController.discordCallback);

// Regular login
router.post('/login', authController.login);

// Logout endpoint
router.post('/logout', authController.logout);

// Password change route - requires authentication
router.put('/change-password', authenticateToken, authController.changePassword);

// Add current user endpoint
router.get('/me', authenticateToken, authController.getCurrentUser);

// Health check endpoint for connection testing
router.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'Auth service is running' });
});

module.exports = router;
