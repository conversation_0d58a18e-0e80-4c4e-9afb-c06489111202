const express = require('express');
const router = express.Router();
const reviewController = require('../controllers/reviewController');
const { authenticateToken, optionalAuth } = require('../middleware/authMiddleware');

// Get reviews for a game - use optional auth to include user reactions if logged in
router.get('/game/:gameId', optionalAuth, reviewController.getGameReviews);

// Submit a new review - requires authentication
router.post('/game/:gameId', authenticateToken, reviewController.submitReview);

// Handle reactions to reviews (likes/dislikes) - requires authentication
router.post('/:reviewId/reactions', authenticateToken, reviewController.handleReviewReaction);

// Get comments for a specific review
router.get('/:reviewId/comments', reviewController.getReviewComments);

// Add a comment to a review - requires authentication
router.post('/:reviewId/comments', authenticateToken, reviewController.addReviewComment);

// Delete a review - requires authentication
router.delete('/:reviewId', authenticateToken, reviewController.deleteReview);

module.exports = router;
