const express = require('express');
const router = express.Router();
const { S3Client, GetObjectCommand } = require('@aws-sdk/client-s3');
const { Readable } = require('stream');

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME;

/**
 * @route GET /games/:gameId/:type/:filename
 * @desc Serve S3 files through your domain
 * @access Public
 * 
 * Example: https://indierepo.com/games/1/images/hover-123.gif
 * Maps to: S3 key "games/1/images/hover-123.gif"
 */
router.get('/games/:gameId/:type/:filename(*)', async (req, res) => {
  try {
    const { gameId, type, filename } = req.params;
    
    // Construct S3 key
    const s3Key = `games/${gameId}/${type}/${filename}`;
    
    console.log(`📁 Serving file: ${s3Key}`);
    
    // Get file from S3
    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: s3Key,
    });
    
    const s3Response = await s3Client.send(command);
    
    // Set appropriate headers
    const contentType = s3Response.ContentType || getContentType(filename);
    const lastModified = s3Response.LastModified;
    const etag = s3Response.ETag;
    
    res.set({
      'Content-Type': contentType,
      'Content-Length': s3Response.ContentLength,
      'Last-Modified': lastModified,
      'ETag': etag,
      'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
      'Access-Control-Allow-Origin': '*', // Allow cross-origin requests
    });
    
    // Handle conditional requests (304 Not Modified)
    const ifNoneMatch = req.headers['if-none-match'];
    if (ifNoneMatch && ifNoneMatch === etag) {
      return res.status(304).end();
    }
    
    // Stream the file to the client
    if (s3Response.Body instanceof Readable) {
      s3Response.Body.pipe(res);
    } else {
      // Handle different body types
      const chunks = [];
      for await (const chunk of s3Response.Body) {
        chunks.push(chunk);
      }
      const buffer = Buffer.concat(chunks);
      res.send(buffer);
    }
    
  } catch (error) {
    console.error(`❌ Error serving file: ${error.message}`);
    
    if (error.name === 'NoSuchKey') {
      res.status(404).json({ message: 'File not found' });
    } else if (error.name === 'AccessDenied') {
      res.status(403).json({ message: 'Access denied' });
    } else {
      res.status(500).json({ message: 'Internal server error' });
    }
  }
});

/**
 * Get content type based on file extension
 */
function getContentType(filename) {
  const ext = filename.toLowerCase().split('.').pop();
  
  const contentTypes = {
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'webp': 'image/webp',
    'svg': 'image/svg+xml',
    'mp4': 'video/mp4',
    'webm': 'video/webm',
    'zip': 'application/zip',
    'rar': 'application/x-rar-compressed',
    'pdf': 'application/pdf',
    'json': 'application/json',
    'txt': 'text/plain',
  };
  
  return contentTypes[ext] || 'application/octet-stream';
}

/**
 * @route GET /uploads/:path(*)
 * @desc Alternative route for legacy upload paths
 * @access Public
 */
router.get('/uploads/:path(*)', async (req, res) => {
  try {
    const s3Key = req.params.path;
    
    // Redirect to the new format or serve directly
    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: s3Key,
    });
    
    const s3Response = await s3Client.send(command);
    
    res.set({
      'Content-Type': s3Response.ContentType || 'application/octet-stream',
      'Content-Length': s3Response.ContentLength,
      'Cache-Control': 'public, max-age=31536000',
    });
    
    if (s3Response.Body instanceof Readable) {
      s3Response.Body.pipe(res);
    } else {
      const chunks = [];
      for await (const chunk of s3Response.Body) {
        chunks.push(chunk);
      }
      const buffer = Buffer.concat(chunks);
      res.send(buffer);
    }
    
  } catch (error) {
    console.error(`❌ Error serving upload: ${error.message}`);
    res.status(404).json({ message: 'File not found' });
  }
});

module.exports = router;
