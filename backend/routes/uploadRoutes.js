const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { authenticateToken } = require('../middleware/authMiddleware');
const uploadController = require('../controllers/uploadController');
const { UPLOAD_CONFIG, validateFileSize, validateGameFileFormat, validateGameFileMimeType } = require('../config/uploadConfig');

// Define storage for temporary files (will be uploaded to S3 and then deleted)
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Create a temporary upload directory
    const tempDir = path.join(__dirname, '../../uploads/temp');
    
    // Create directory if it doesn't exist
    fs.mkdirSync(tempDir, { recursive: true });
    
    cb(null, tempDir);
  },
  filename: function (req, file, cb) {
    // Generate unique filename for temporary storage
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `${file.fieldname}-${uniqueSuffix}${ext}`);
  }
});

// File filter to validate upload types
const fileFilter = function (req, file, cb) {
  console.log(`File filter processing field: ${file.fieldname}, mimetype: ${file.mimetype}, filename: ${file.originalname}`);
  
  // Define allowed field names
  const allowedFields = [
    'gameFile_0', 'gameFile_1', 'gameFile_2', 'gameFile_3', 'gameFile_4',
    'coverImage', 'cardImage', 'gifAnimation',
    'screenshot_0', 'screenshot_1', 'screenshot_2', 'screenshot_3', 'screenshot_4'
  ];
  
  // Check if field name is allowed
  if (!allowedFields.includes(file.fieldname)) {
    console.log(`Rejecting unknown field: ${file.fieldname}`);
    return cb(new Error(`Unknown field type: ${file.fieldname}. Allowed fields: ${allowedFields.join(', ')}`), false);
  }
  
  // Skip file validation for external links by checking gameFilesMetadata
  if (req.body && req.body.gameFilesMetadata && file.fieldname.startsWith('gameFile_')) {
    try {
      const gameFilesMetadata = JSON.parse(req.body.gameFilesMetadata || '[]');
      // Extract index from fieldname (e.g., 'gameFile_0' -> 0)
      const fileIndex = parseInt(file.fieldname.split('_')[1] || '0', 10);
      
      if (gameFilesMetadata[fileIndex] && gameFilesMetadata[fileIndex].isExternalLink) {
        console.log(`Allowing external link for ${file.fieldname}`);
        return cb(null, true);
      }
    } catch (err) {
      console.error('Error parsing game files metadata:', err);
    }
  }
  
  // Handle different field types
  if (file.fieldname === 'coverImage' ||
      file.fieldname === 'cardImage' ||
      file.fieldname.startsWith('screenshot_') ||
      file.fieldname === 'gifAnimation') {
    // Image validation
    if (!file.mimetype.startsWith('image/')) {
      console.log(`Rejecting non-image file for ${file.fieldname}: ${file.mimetype}`);
      return cb(new Error('Only image files are allowed!'), false);
    }
    
    // Additional validation for GIF files
    if (file.fieldname === 'gifAnimation' && file.mimetype !== 'image/gif') {
      console.log(`Rejecting non-GIF file for gifAnimation: ${file.mimetype}`);
      return cb(new Error('Only GIF files are allowed for animations!'), false);
    }
  } else if (file.fieldname.startsWith('gameFile_')) {
    // Game file validation - allow various file types
    const allowedGameExts = ['.zip', '.rar', '.7z', '.exe', '.dmg', '.html', '.jar', '.apk'];
    const ext = path.extname(file.originalname).toLowerCase();
    
    if (!allowedGameExts.includes(ext)) {
      console.log(`Rejecting invalid game file extension: ${ext}`);
      return cb(new Error(`Invalid game file type! Allowed: ${allowedGameExts.join(', ')}`), false);
    }
  }
  
  // If all validations pass
  console.log(`Accepting file: ${file.fieldname}`);
  cb(null, true);
};

// Initialize multer with settings optimized for S3 uploads
const upload = multer({
  storage: storage,
  // fileFilter: fileFilter, // Temporarily disabled to debug the issue
  limits: {
    fileSize: UPLOAD_CONFIG.MAX_FILE_SIZE, // 1GB max file size
    files: 5 // Reduced to handle single game file + card image + gif
  }
});

// Error handler for multer
const handleMulterError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        message: `File is too large. Maximum size is ${UPLOAD_CONFIG.MAX_FILE_SIZE_MB}MB per file.`
      });
    } else if (err.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({ 
        message: 'Too many files. Maximum allowed is 15 files per upload.' 
      });
    } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
      console.warn(`Unexpected field: ${err.field}`);
      // For unexpected fields, log them but don't stop the upload
      // This might be a form field rather than a file
      return res.status(400).json({ 
        message: `Unexpected field: ${err.field}. Please check your form field names.`,
        expectedFields: [
          'gameFile', 'cardImage', 'gifAnimation'
        ]
      });
    }
    
    return res.status(400).json({ message: err.message });
  } else if (err) {
    return res.status(400).json({ message: err.message });
  }
  
  next();
};

// Middleware to handle single file uploads
const handleSingleUpload = (req, res, next) => {
  upload.single('file')(req, res, function(err) {
    if (err instanceof multer.MulterError) {
      console.error('Multer error:', err);
      return res.status(400).json({ message: err.message });
    } else if (err) {
      console.error('Unknown upload error:', err);
      return res.status(500).json({ message: err.message });
    }
    next();
  });
};

// Middleware to handle avatar uploads
const handleAvatarUpload = (req, res, next) => {
  upload.single('avatar')(req, res, function(err) {
    if (err) {
      console.error('Avatar upload error:', err);
      return res.status(400).json({ message: err.message });
    }
    
    if (!req.file) {
      return res.status(400).json({ message: 'No avatar file uploaded' });
    }
    
    next();
  });
};

// Middleware to handle game uploads with multiple files
const handleGameUpload = (req, res, next) => {
  // Debug logging to see what fields are being sent
  console.log('Game upload middleware triggered');
  
  // Use upload.any() to accept any file field and handle validation ourselves
  upload.any()(req, res, function(err) {
    // Debug logging
    console.log('Multer processing completed');
    console.log('Request body keys:', Object.keys(req.body || {}));
    console.log('Request files:', req.files ? req.files.map(f => ({ fieldname: f.fieldname, originalname: f.originalname })) : []);
    
    // Handle multer errors
    if (err) {
      console.error('Multer error occurred:', err);
      return handleMulterError(err, req, res, next);
    }
    
    // Convert files array to object for easier access (like upload.fields() does)
    if (req.files && req.files.length > 0) {
      req.files = req.files.reduce((acc, file) => {
        if (!acc[file.fieldname]) {
          acc[file.fieldname] = [];
        }
        acc[file.fieldname].push(file);
        return acc;
      }, {});
    }
    
    // Validate allowed field names and file types
    const allowedFields = [
      'gameFile', 'cardImage', 'gifAnimation'
    ];
    
    if (req.files) {
      const fileFields = Object.keys(req.files);
      const invalidFields = fileFields.filter(field => !allowedFields.includes(field));
      
      if (invalidFields.length > 0) {
        return res.status(400).json({
          message: `Invalid field names: ${invalidFields.join(', ')}`,
          allowedFields: allowedFields
        });
      }
      
      // Validate file types for each field
      for (const [fieldName, files] of Object.entries(req.files)) {
        const file = files[0]; // Get first file from array
        
        // Image field validation
        if (['cardImage', 'gifAnimation'].includes(fieldName)) {
          if (!file.mimetype.startsWith('image/')) {
            return res.status(400).json({
              message: `Invalid file type for ${fieldName}. Only image files are allowed.`
            });
          }

          // Special validation for GIF animation
          if (fieldName === 'gifAnimation' && file.mimetype !== 'image/gif') {
            return res.status(400).json({
              message: 'Only GIF files are allowed for animations.'
            });
          }
        }
        
        // Game file validation (web games only - ZIP format)
        if (fieldName === 'gameFile') {
          // Validate file format
          if (!validateGameFileFormat(file.originalname)) {
            return res.status(400).json({
              message: `Invalid game file type. Only ZIP files are allowed for web games.`
            });
          }

          // Validate MIME type
          if (!validateGameFileMimeType(file.mimetype)) {
            return res.status(400).json({
              message: `Invalid file format. Only ZIP files are allowed for web games.`
            });
          }

          // Validate file size
          if (!validateFileSize(file.size)) {
            return res.status(400).json({
              message: `File is too large. Maximum size is ${UPLOAD_CONFIG.MAX_FILE_SIZE_MB}MB.`
            });
          }
        }
      }
    }
    
    // Validate that we have a game file upload
    const hasGameFile = req.files && req.files.gameFile;

    if (!hasGameFile) {
      return res.status(400).json({
        message: 'Game file upload is required'
      });
    }
    
    next();
  });
};

// Routes

// Game upload route with multiple files (main route for UploadGamePage)
// IMPORTANT: This must come BEFORE the generic /:type route
router.post('/game', authenticateToken, handleGameUpload, uploadController.uploadGame);

// Avatar upload removed - users can only select from predefined avatars

// Generic file upload route (for single files)
// IMPORTANT: This MUST come AFTER all specific routes like /game and /avatar
router.post('/:type', authenticateToken, handleSingleUpload, uploadController.uploadFile);

// Health check for S3 configuration
router.get('/s3-status', authenticateToken, async (req, res) => {
  try {
    const { checkS3Configuration } = require('../services/s3Service');
    const isS3Ready = await checkS3Configuration();
    
    res.json({
      s3Enabled: isS3Ready,
      message: isS3Ready ? 'S3 is properly configured' : 'S3 configuration error'
    });
  } catch (error) {
    res.status(500).json({
      s3Enabled: false,
      message: 'Error checking S3 configuration',
      error: error.message
    });
  }
});

module.exports = router;
