const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const pool = require('../config/database');
// Update this import to use the correct middleware path
const { authenticateToken, optionalAuth } = require('../middleware/authMiddleware');

// Get download info and track download
router.get('/games/files/:fileId', optionalAuth, async (req, res) => {
  const { fileId } = req.params;
  const userId = req.user ? req.user.id : null;
  
  try {
    // Get file info from database
    const [files] = await pool.query(
      'SELECT f.*, g.priceModel, g.price, g.creditPrice FROM GameFiles f JOIN Games g ON f.gameId = g.id WHERE f.id = ?',
      [fileId]
    );
    
    if (!files.length) {
      return res.status(404).json({ message: 'File not found' });
    }
    
    const file = files[0];
    
    // Check if file requires purchase
    if (file.requiresPurchase) {
      // If file requires purchase and not authenticated, deny access
      if (!userId) {
        return res.status(401).json({ message: 'Authentication required for this file' });
      }
      
      // Check if user has purchased the game
      const [purchases] = await pool.query(
        'SELECT * FROM User_Games WHERE userId = ? AND gameId = ? AND purchaseType IS NOT NULL',
        [userId, file.gameId]
      );
      
      if (!purchases.length) {
        return res.status(403).json({ message: 'Game purchase required to download this file' });
      }
    }
    
    // Track the download
    await pool.query(
      'UPDATE Games SET downloadCount = downloadCount + 1 WHERE id = ?',
      [file.gameId]
    );
    
    // If user is logged in, track user download
    if (userId) {
      // Check if user has this game in library
      const [userGames] = await pool.query(
        'SELECT * FROM User_Games WHERE userId = ? AND gameId = ?',
        [userId, file.gameId]
      );
      
      if (!userGames.length) {
        // Add to user library
        await pool.query(
          'INSERT INTO User_Games (userId, gameId, inLibrary) VALUES (?, ?, TRUE)',
          [userId, file.gameId]
        );
      } else if (!userGames[0].inLibrary) {
        // Update existing entry to add to library
        await pool.query(
          'UPDATE User_Games SET inLibrary = TRUE WHERE userId = ? AND gameId = ?',
          [userId, file.gameId]
        );
      }
    }
    
    // Return download URL
    res.json({
      fileName: file.fileName,
      downloadUrl: file.isExternalLink ? file.externalUrl : `/uploads/games/${file.gameId}/files/${path.basename(file.filePath)}`,
      fileSize: file.fileSize
    });
  } catch (error) {
    console.error('Error handling download:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

module.exports = router;
