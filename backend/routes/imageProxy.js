const express = require('express');
const router = express.Router();
const { generatePresignedUrl } = require('../services/s3Service');
const rateLimit = require('express-rate-limit');

// Rate limiting for image requests
const imageRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs
  message: 'Too many image requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * @route GET /api/images/:gameId/:type/:filename
 * @desc Get secure image URL with access control
 * @access Public (but with rate limiting)
 */
router.get('/:gameId/:type/:filename', imageRateLimit, async (req, res) => {
  try {
    const { gameId, type, filename } = req.params;
    
    // Validate parameters
    if (!gameId || !type || !filename) {
      return res.status(400).json({ message: 'Missing required parameters' });
    }
    
    // Validate file type (security check)
    const allowedTypes = ['images', 'videos', 'files'];
    if (!allowedTypes.includes(type)) {
      return res.status(400).json({ message: 'Invalid file type' });
    }
    
    // Validate file extension (security check)
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.mp4', '.webm', '.zip', '.rar'];
    const fileExtension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    if (!allowedExtensions.includes(fileExtension)) {
      return res.status(400).json({ message: 'Invalid file type' });
    }
    
    // Construct S3 key
    const s3Key = `games/${gameId}/${type}/${filename}`;
    
    // Generate presigned URL (valid for 1 hour)
    const presignedUrl = await generatePresignedUrl(s3Key, 3600);
    
    // Option 1: Redirect to presigned URL
    res.redirect(presignedUrl);
    
    // Option 2: Return JSON with URL (uncomment if you prefer this)
    // res.json({ url: presignedUrl, expiresIn: 3600 });
    
  } catch (error) {
    console.error('Error serving image:', error);
    res.status(500).json({ message: 'Failed to serve image' });
  }
});

/**
 * @route GET /api/images/secure/:gameId/:type/:filename
 * @desc Get secure image URL with user authentication
 * @access Private (requires authentication)
 */
router.get('/secure/:gameId/:type/:filename', async (req, res) => {
  try {
    // Add authentication check here
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ message: 'Authentication required' });
    }
    
    // Verify JWT token (implement your auth logic)
    // const token = authHeader.split(' ')[1];
    // const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    const { gameId, type, filename } = req.params;
    const s3Key = `games/${gameId}/${type}/${filename}`;
    
    // Generate shorter-lived presigned URL for authenticated users
    const presignedUrl = await generatePresignedUrl(s3Key, 900); // 15 minutes
    
    res.redirect(presignedUrl);
    
  } catch (error) {
    console.error('Error serving secure image:', error);
    res.status(500).json({ message: 'Failed to serve secure image' });
  }
});

module.exports = router;
