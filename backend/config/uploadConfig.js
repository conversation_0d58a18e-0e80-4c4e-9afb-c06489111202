// Backend upload configuration constants
const UPLOAD_CONFIG = {
  // Maximum file size in bytes (1GB = 1024 * 1024 * 1024)
  MAX_FILE_SIZE: 1024 * 1024 * 1024, // 1GB
  
  // Maximum file size in MB for display purposes
  MAX_FILE_SIZE_MB: 1024,
  
  // Minimum description length
  MIN_DESCRIPTION_LENGTH: 50,
  
  // Allowed game file formats (web games only)
  ALLOWED_GAME_FORMATS: ['.zip'],
  
  // Allowed MIME types for game files
  ALLOWED_GAME_MIME_TYPES: [
    'application/zip',
    'application/x-zip-compressed',
    'application/octet-stream'
  ],
  
  // Required files in ZIP for web games
  REQUIRED_ZIP_FILES: {
    INDEX_HTML: 'index.html',
    COMPRESSION_EXTENSIONS: ['.gzip', '.gz', '.br']
  },
  
  // Allowed web game types
  ALLOWED_WEB_GAME_TYPES: ['html5', 'webgl', 'unity'],
  
  // S3 configuration for game files
  S3_GAME_PATH_PREFIX: 'games',
  S3_EXTRACTED_PATH_PREFIX: 'extracted-games'
};

// Helper function to format file size
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Helper function to validate file size
const validateFileSize = (fileSize) => {
  return fileSize <= UPLOAD_CONFIG.MAX_FILE_SIZE;
};

// Helper function to validate file format
const validateGameFileFormat = (filename) => {
  const extension = '.' + filename.split('.').pop().toLowerCase();
  return UPLOAD_CONFIG.ALLOWED_GAME_FORMATS.includes(extension);
};

// Helper function to validate MIME type
const validateGameFileMimeType = (mimetype) => {
  return UPLOAD_CONFIG.ALLOWED_GAME_MIME_TYPES.includes(mimetype);
};

module.exports = {
  UPLOAD_CONFIG,
  formatFileSize,
  validateFileSize,
  validateGameFileFormat,
  validateGameFileMimeType
};
