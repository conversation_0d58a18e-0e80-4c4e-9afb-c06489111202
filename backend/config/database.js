const knex = require('knex');
const knexConfig = require('../knexfile');
require('dotenv').config();
const logger = require('./logger');

// Get the appropriate configuration based on NODE_ENV
const environment = process.env.NODE_ENV || 'development';
const config = knexConfig[environment];

logger.info('Initializing Knex database connection', {
  host: config.connection.host,
  database: config.connection.database,
  user: config.connection.user ? 'Set' : 'Not set',
  environment: environment
});

// Initialize Knex instance
const db = knex(config);

// Test the connection and set up error handling
async function validateConnection() {
  try {
    // Test a simple query
    const result = await db.raw('SELECT 1 as test');
    logger.info('PostgreSQL connection established successfully');
    logger.info('Test query executed successfully', result.rows);
    
    return true;
  } catch (err) {
    logger.error('Error connecting to PostgreSQL:', {
      error: err.message,
      host: config.connection.host,
      user: config.connection.user,
      database: config.connection.database
    });
    return false;
  }
}

// Run the connection test
validateConnection();

// Graceful shutdown
process.on('SIGINT', () => {
  db.destroy(() => {
    logger.info('Database connection closed.');
    process.exit(0);
  });
});

module.exports = db;