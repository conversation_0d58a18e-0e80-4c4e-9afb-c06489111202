const logger = require('./logger');

/**
 * CORS configuration for the application
 * <PERSON>les cross-origin requests for the main domain
 */
const corsConfig = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps, curl requests)
    const allowedOrigins = [
      'http://localhost:5173',  // Vite dev server
      'https://indierepo.com',   // Production domain with SSL
      'https://www.indierepo.com', // www subdomain
      process.env.FRONTEND_URL
    ].filter(Boolean);  // Filter out undefined values
    
    // Allow development origins
    const isDev = process.env.NODE_ENV !== 'production';
    
    if (!origin) {
      // Allow requests with no origin
      return callback(null, true);
    }
    
    // Log all incoming origins for debugging
    logger.debug(`Incoming request from origin: ${origin}`);
    
    // Check if this is a development environment or allowed origin
    if (isDev || allowedOrigins.includes(origin)) {
      logger.debug(`Allowing access from origin: ${origin}`);
      return callback(null, true);
    }
    
    // Check if origin is main domain or www subdomain
    if (origin.includes('indierepo.com')) {
      logger.debug(`Allowing access from indierepo.com domain: ${origin}`);
      return callback(null, true);
    }
    
    logger.warn(`Origin ${origin} not allowed by CORS`);
    callback(null, false);  // Don't allow
  },
  credentials: true,  // Allow credentials (cookies, auth headers)
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

module.exports = corsConfig; 