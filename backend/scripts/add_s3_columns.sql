-- ====================================================================
-- S3 MIGRATION SCRIPT - FOR EXISTING DATABASES ONLY
-- ====================================================================
-- 
-- IMPORTANT: This script is ONLY needed for databases created before 
-- the S3 integration was added to the main createDB.sql schema.
--
-- If you're setting up a fresh database, simply run createDB.sql 
-- which already includes all S3 columns and tables.
--
-- This migration adds S3 support to existing IndieRepo databases
-- ====================================================================

-- Check if this is a fresh database or existing one
-- If SystemConfig table doesn't exist, this might be an old database
SET @table_exists = (
    SELECT COUNT(*)
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'SystemConfig'
);

-- Only proceed if SystemConfig doesn't exist (indicating old database)
-- or if it exists but s3_migration_status is 'not_started'
SET @should_migrate = (
    SELECT CASE 
        WHEN @table_exists = 0 THEN 1
        WHEN (SELECT config_value FROM SystemConfig WHERE config_key = 's3_migration_status') = 'not_started' THEN 1
        ELSE 0
    END
);

-- Exit if migration not needed
SELECT CASE @should_migrate
    WHEN 0 THEN 'Migration not needed - S3 columns already exist or migration already completed'
    ELSE 'Proceeding with S3 migration...'
END as migration_status;

-- Only proceed if migration is needed
-- Note: The following commands will only execute if run in a context where @should_migrate = 1

-- Add S3 columns to existing tables
ALTER TABLE GameFiles ADD COLUMN IF NOT EXISTS s3Key VARCHAR(500) NULL COMMENT 'AWS S3 object key for file operations';
ALTER TABLE GameImages ADD COLUMN IF NOT EXISTS s3Key VARCHAR(500) NULL COMMENT 'AWS S3 object key for image operations';
ALTER TABLE UserAvatars ADD COLUMN IF NOT EXISTS s3Key VARCHAR(500) NULL COMMENT 'AWS S3 object key for avatar operations';

-- Update GameImages imageType enum to include 'screenshot' if not already present
ALTER TABLE GameImages MODIFY COLUMN imageType ENUM('cover', 'card', 'gif', 'screenshot') NOT NULL;

-- Create SystemConfig table if it doesn't exist
CREATE TABLE IF NOT EXISTS SystemConfig (
  id INT AUTO_INCREMENT PRIMARY KEY,
  config_key VARCHAR(100) NOT NULL UNIQUE,
  config_value TEXT,
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert or update system configuration
INSERT INTO SystemConfig (config_key, config_value, description) VALUES
('db_version', '1.0', 'Current database schema version'),
('s3_migration_status', 'completed', 'Status of S3 migration (not_started, in_progress, completed)'),
('storage_backend', 'local', 'Current storage backend (local, s3, hybrid)')
ON DUPLICATE KEY UPDATE 
    config_value = CASE 
        WHEN config_key = 's3_migration_status' THEN 'completed'
        ELSE VALUES(config_value)
    END,
    updated_at = CURRENT_TIMESTAMP;

-- Create indexes for S3 columns if they don't exist
CREATE INDEX IF NOT EXISTS idx_gamefiles_s3key ON GameFiles(s3Key);
CREATE INDEX IF NOT EXISTS idx_gameimages_s3key ON GameImages(s3Key);
CREATE INDEX IF NOT EXISTS idx_useravatars_s3key ON UserAvatars(s3Key);
CREATE INDEX IF NOT EXISTS idx_systemconfig_key ON SystemConfig(config_key);

-- Display completion message
SELECT 'S3 migration completed successfully! S3 columns and indexes have been added.' as result; 