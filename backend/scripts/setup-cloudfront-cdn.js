#!/usr/bin/env node

/**
 * Setup CloudFront CDN for S3 Bucket
 * 
 * This script helps configure CloudFront to serve S3 content securely
 * Usage: node scripts/setup-cloudfront-cdn.js
 */

const { CloudFrontClient, CreateDistributionCommand, GetDistributionCommand } = require('@aws-sdk/client-cloudfront');

const cloudFrontConfig = {
  region: 'us-east-1', // CloudFront is global but we specify us-east-1
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
};

const client = new CloudFrontClient(cloudFrontConfig);

async function setupCloudFront() {
  console.log('🚀 Setting up CloudFront CDN for IndieRepo');
  console.log('=========================================\n');

  const distributionConfig = {
    CallerReference: `indierepo-cdn-${Date.now()}`,
    Comment: 'IndieRepo S3 Content Distribution',
    DefaultCacheBehavior: {
      TargetOriginId: 'indierepo-s3-origin',
      ViewerProtocolPolicy: 'redirect-to-https',
      TrustedSigners: {
        Enabled: false,
        Quantity: 0,
      },
      ForwardedValues: {
        QueryString: false,
        Cookies: {
          Forward: 'none',
        },
      },
      MinTTL: 0,
      DefaultTTL: 86400, // 1 day
      MaxTTL: 31536000, // 1 year
    },
    Origins: {
      Quantity: 1,
      Items: [
        {
          Id: 'indierepo-s3-origin',
          DomainName: 'indierepo.com.s3.us-east-1.amazonaws.com',
          CustomOriginConfig: {
            HTTPPort: 80,
            HTTPSPort: 443,
            OriginProtocolPolicy: 'https-only',
          },
        },
      ],
    },
    Enabled: true,
    PriceClass: 'PriceClass_100', // Use only North America and Europe edge locations
    Aliases: {
      Quantity: 1,
      Items: ['cdn.indierepo.com'], // Your custom domain
    },
    ViewerCertificate: {
      CloudFrontDefaultCertificate: true, // Use this initially, then switch to ACM certificate
    },
  };

  try {
    console.log('📦 Creating CloudFront distribution...');
    const command = new CreateDistributionCommand({
      DistributionConfig: distributionConfig,
    });

    const result = await client.send(command);
    
    console.log('✅ CloudFront distribution created successfully!');
    console.log('📋 Distribution Details:');
    console.log(`- Distribution ID: ${result.Distribution.Id}`);
    console.log(`- Domain Name: ${result.Distribution.DomainName}`);
    console.log(`- Status: ${result.Distribution.Status}`);
    
    console.log('\n📝 Next Steps:');
    console.log('1. Wait for distribution to deploy (15-20 minutes)');
    console.log('2. Update your environment variables:');
    console.log(`   UPLOADS_BASE_URL=https://${result.Distribution.DomainName}`);
    console.log('3. Optional: Set up custom domain (cdn.indierepo.com) with SSL certificate');
    console.log('4. Update your frontend to use the new CDN URLs');
    
    return result.Distribution;
  } catch (error) {
    console.error('❌ Error creating CloudFront distribution:', error);
    throw error;
  }
}

// Configuration instructions
function printConfigurationGuide() {
  console.log('\n🔧 Manual Configuration Guide');
  console.log('=============================');
  console.log('\nIf you prefer to set up CloudFront manually:');
  console.log('1. Go to AWS CloudFront Console');
  console.log('2. Create a new distribution');
  console.log('3. Set origin domain: indierepo.com.s3.us-east-1.amazonaws.com');
  console.log('4. Set viewer protocol policy: Redirect HTTP to HTTPS');
  console.log('5. Set cache behavior for /games/* path pattern');
  console.log('6. Enable compression');
  console.log('7. Set appropriate TTL values');
  console.log('\nBenefits:');
  console.log('✅ Faster content delivery');
  console.log('✅ Reduced S3 bandwidth costs');
  console.log('✅ Better security (hides S3 structure)');
  console.log('✅ DDoS protection');
  console.log('✅ Custom domain support');
}

if (require.main === module) {
  // Check if AWS credentials are available
  if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
    console.error('❌ AWS credentials not found in environment variables');
    console.log('Please ensure AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY are set');
    printConfigurationGuide();
    process.exit(1);
  }

  setupCloudFront()
    .then(() => {
      console.log('\n🎉 CloudFront setup completed!');
    })
    .catch((error) => {
      console.error('Setup failed:', error.message);
      printConfigurationGuide();
      process.exit(1);
    });
}
