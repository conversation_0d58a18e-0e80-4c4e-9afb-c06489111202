#!/usr/bin/env node

/**
 * Database Initialization Script for IndieRepo
 * Creates the database if it doesn't exist and runs migrations
 */

const dotenv = require('dotenv');
const path = require('path');
const { Client } = require('pg');
const knexfile = require('../knexfile');
const knex = require('knex');

// Load environment-specific .env file based on NODE_ENV
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env';
console.log(`Init DB: Loading environment from ${envFile}`);
dotenv.config({ path: path.resolve(process.cwd(), envFile) });

async function initDb() {
  console.log('🔧 IndieRepo Database Initialization');
  console.log('====================================');
  
  // Get DB connection info
  const dbHost = process.env.DB_HOST;
  const dbPort = process.env.DB_PORT || 5432;
  const dbUser = process.env.DB_USER;
  const dbPassword = process.env.DB_PASSWORD;
  const dbName = process.env.DB_NAME;
  
  console.log(`Using database configuration:
  - Host: ${dbHost}
  - Port: ${dbPort}
  - User: ${dbUser}
  - Database: ${dbName}
  - Environment: ${process.env.NODE_ENV || 'development'}
  `);
  
  try {
    // Connect to postgres database to create our database if needed
    const client = new Client({
      host: dbHost,
      port: dbPort,
      user: dbUser,
      password: dbPassword,
      database: 'postgres', // Connect to default postgres DB first
      ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
    });
    
    console.log('Connecting to PostgreSQL server...');
    await client.connect();
    console.log('✅ Connected to PostgreSQL server');
    
    // Check if our database exists
    const dbCheckResult = await client.query(`
      SELECT 1 FROM pg_database WHERE datname = $1
    `, [dbName]);
    
    if (dbCheckResult.rows.length === 0) {
      console.log(`Database "${dbName}" doesn't exist, creating it...`);
      // Need to escape identifiers to prevent SQL injection
      const escapedDbName = dbName.replace(/[^a-zA-Z0-9_]/g, '');
      await client.query(`CREATE DATABASE "${escapedDbName}"`);
      console.log(`✅ Database "${dbName}" created successfully`);
    } else {
      console.log(`✅ Database "${dbName}" already exists`);
    }
    
    // Close postgres client
    await client.end();
    console.log('Connection to postgres closed');
    
    // Run migrations using Knex
    console.log('Running migrations...');
    const environment = process.env.NODE_ENV || 'development';
    const config = knexfile[environment];
    
    if (!config) {
      throw new Error(`Invalid environment: ${environment}`);
    }
    
    const db = knex(config);
    const [batchNo, log] = await db.migrate.latest();
    
    if (log.length === 0) {
      console.log('✅ Database is already up to date');
    } else {
      console.log(`✅ Batch ${batchNo} run: ${log.length} migrations`);
      console.log(log.join('\n'));
    }
    
    // Show migration status
    try {
      const migrationStatus = await db.migrate.status();
      // migrationStatus returns [currentBatch, completedMigrations] or just completedMigrations
      const completedMigrations = Array.isArray(migrationStatus[0]) ? migrationStatus[1] : migrationStatus;

      if (completedMigrations && completedMigrations.length > 0) {
        console.log(`\nCompleted migrations: ${completedMigrations.length}`);
        console.log('Latest migrations:');
        completedMigrations.slice(-5).forEach(name => {
          console.log(`- ${name}`);
        });
      } else {
        console.log('\nNo completed migrations found.');
      }
    } catch (statusError) {
      console.log('\nCould not retrieve migration status:', statusError.message);
    }
    
    await db.destroy();
    console.log('\n✅ Database initialization complete!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Database initialization failed:');
    console.error(error);
    process.exit(1);
  }
}

// Run initialization
initDb();
