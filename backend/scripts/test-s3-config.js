#!/usr/bin/env node

/**
 * Test S3 Configuration Script
 * 
 * This script tests the AWS S3 configuration and provides detailed feedback
 * about any issues found.
 */

// Load production environment if available
const path = require('path');
const fs = require('fs');

const prodEnvPath = path.join(__dirname, '../.env.production');
if (fs.existsSync(prodEnvPath)) {
  require('dotenv').config({ path: prodEnvPath });
  console.log('📁 Loaded .env.production file');
} else {
  require('dotenv').config();
  console.log('📁 Loaded default .env file');
}

const { checkS3Configuration } = require('../services/s3Service');

async function testS3Configuration() {
  console.log('🔍 Testing AWS S3 Configuration...\n');
  
  // Check environment variables
  console.log('📋 Environment Variables:');
  const requiredVars = [
    'AWS_REGION',
    'AWS_ACCESS_KEY_ID', 
    'AWS_SECRET_ACCESS_KEY',
    'AWS_S3_BUCKET_NAME'
  ];
  
  const optionalVars = [
    'STORAGE_TYPE',
    'UPLOADS_BASE_URL'
  ];
  
  let missingVars = [];
  
  // Check required variables
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      // Mask sensitive values
      const displayValue = varName.includes('SECRET') || varName.includes('KEY') 
        ? `${value.substring(0, 4)}****${value.substring(value.length - 4)}`
        : value;
      console.log(`✅ ${varName}: ${displayValue}`);
    } else {
      console.log(`❌ ${varName}: NOT SET`);
      missingVars.push(varName);
    }
  });
  
  // Check optional variables
  optionalVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`✅ ${varName}: ${value}`);
    } else {
      console.log(`⚠️ ${varName}: NOT SET (optional)`);
    }
  });
  
  if (missingVars.length > 0) {
    console.log(`\n❌ Missing required environment variables: ${missingVars.join(', ')}`);
    console.log('\n💡 Please check your .env.production file and ensure all AWS S3 variables are set.');
    return false;
  }
  
  console.log('\n🔗 Testing S3 Connection...');
  
  try {
    const isS3Ready = await checkS3Configuration();
    
    if (isS3Ready) {
      console.log('✅ S3 configuration is working correctly!');
      console.log('\n📝 Next steps:');
      console.log('1. Your S3 bucket is accessible');
      console.log('2. Upload functionality should work');
      console.log('3. Test uploading a game to verify end-to-end functionality');
      return true;
    } else {
      console.log('❌ S3 configuration test failed');
      return false;
    }
  } catch (error) {
    console.error('❌ S3 configuration test failed with error:', error.message);
    
    // Provide specific guidance based on error type
    if (error.message.includes('NoSuchBucket')) {
      console.log('\n💡 Bucket does not exist. Please:');
      console.log('1. Create the S3 bucket in AWS Console');
      console.log('2. Ensure the bucket name matches AWS_S3_BUCKET_NAME');
      console.log('3. Set appropriate bucket permissions for public read access');
    } else if (error.message.includes('AccessDenied')) {
      console.log('\n💡 Access denied. Please check:');
      console.log('1. AWS credentials are correct');
      console.log('2. IAM user has S3 permissions (s3:PutObject, s3:GetObject, s3:DeleteObject)');
      console.log('3. Bucket policy allows your IAM user access');
    } else if (error.message.includes('InvalidAccessKeyId')) {
      console.log('\n💡 Invalid access key. Please:');
      console.log('1. Verify AWS_ACCESS_KEY_ID is correct');
      console.log('2. Ensure the IAM user exists and is active');
    } else if (error.message.includes('SignatureDoesNotMatch')) {
      console.log('\n💡 Invalid secret key. Please:');
      console.log('1. Verify AWS_SECRET_ACCESS_KEY is correct');
      console.log('2. Ensure there are no extra spaces or characters');
    }
    
    return false;
  }
}

// Run the test
if (require.main === module) {
  testS3Configuration()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { testS3Configuration };
