const fs = require('fs');
const path = require('path');

/**
 * Ensures that all required directories for the application exist
 */
function ensureDirectories() {
  const directories = [
    // Uploads directories
    '../uploads',
    '../uploads/avatars',
    '../uploads/games',
    '../uploads/temp',
    
    // Placeholder directories
    '../public',
    '../public/placeholders',
  ];

  // Create each directory if it doesn't exist
  directories.forEach(dir => {
    const dirPath = path.join(__dirname, dir);
    if (!fs.existsSync(dirPath)) {
      console.log(`Creating directory: ${dirPath}`);
      fs.mkdirSync(dirPath, { recursive: true });
    }
  });

  // Create basic placeholder files if they don't exist
  const placeholderDir = path.join(__dirname, '../public/placeholders');
  const placeholders = ['cover.jpg', 'card.jpg', 'gif.jpg', 'default.jpg'];
  
  placeholders.forEach(filename => {
    const filePath = path.join(placeholderDir, filename);
    if (!fs.existsSync(filePath)) {
      console.log(`Creating empty placeholder file: ${filePath}`);
      fs.writeFileSync(filePath, '');
    }
  });

  console.log('Directory structure verified!');
}

// Run on import
ensureDirectories();

module.exports = ensureDirectories;
