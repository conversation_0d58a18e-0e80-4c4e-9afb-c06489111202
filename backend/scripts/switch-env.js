/**
 * Environment Switcher Script
 * 
 * This script allows easy switching between development and production environments
 * Usage: 
 *   - Development: node scripts/switch-env.js dev
 *   - Production: node scripts/switch-env.js prod
 */

const fs = require('fs');
const path = require('path');

// Define paths
const rootDir = path.join(__dirname, '..');
const envPath = path.join(rootDir, '.env');
const envDevPath = path.join(rootDir, '.env.development');
const envProdPath = path.join(rootDir, '.env.production');

// Get environment argument
const args = process.argv.slice(2);
const env = args[0]?.toLowerCase() || '';

// Validate input
if (!['dev', 'development', 'prod', 'production'].includes(env)) {
  console.error('Invalid environment. Use "dev" or "prod"');
  console.log('Usage: node scripts/switch-env.js [dev|prod]');
  process.exit(1);
}

// Determine target environment file
const isDev = ['dev', 'development'].includes(env);
const sourcePath = isDev ? envDevPath : envProdPath;
const envType = isDev ? 'DEVELOPMENT' : 'PRODUCTION';

// Check if source file exists
if (!fs.existsSync(sourcePath)) {
  console.error(`Environment file ${sourcePath} not found!`);
  process.exit(1);
}

try {
  // Read the source file
  const sourceContent = fs.readFileSync(sourcePath, 'utf8');
  
  // Create header for the main .env file
  const header = `# Environment Configuration
# Using separate .env files for each environment
# Current environment: ${envType}
# To switch environments, run: node scripts/switch-env.js [dev|prod]

# This file is automatically generated - DO NOT MODIFY DIRECTLY
# Instead, modify .env.development or .env.production

# ================================================
# Current Environment Configuration (${envType})
# ================================================

`;

  // Write the combined content to .env
  fs.writeFileSync(envPath, header + sourceContent);
  
  console.log(`✓ Successfully switched to ${envType} environment`);
  console.log(`Environment configuration loaded from ${path.basename(sourcePath)}`);
  
} catch (error) {
  console.error(`Error switching environments: ${error.message}`);
  process.exit(1);
}
