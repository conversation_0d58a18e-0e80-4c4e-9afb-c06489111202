const mysql = require('mysql2');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

/**
 * <PERSON>ript to initialize the database with tables and sample data
 * Run this once to set up your RDS database
 */

const connection = mysql.createConnection({
  host: process.env.DB_HOST || 'indierepojack.cy544o8qq9ug.us-east-1.rds.amazonaws.com',
  user: process.env.DB_USER || 'admin',
  password: process.env.DB_PASSWORD,
  multipleStatements: true // Allow multiple SQL statements
});

// Path to SQL file
const sqlFilePath = path.join(__dirname, '../../createDB.sql');

console.log('🔧 Database Setup Script');
console.log('========================');
console.log(`Host: ${connection.config.host}`);
console.log(`User: ${connection.config.user}`);

// Read SQL file
console.log('\n📖 Reading SQL file...');
const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

// Connect to MySQL
console.log('🔌 Connecting to RDS MySQL...');
connection.connect((err) => {
  if (err) {
    console.error('❌ Connection failed:', err.message);
    process.exit(1);
  }
  
  console.log('✅ Connected successfully!');
  
  // Execute SQL
  console.log('\n🚀 Executing SQL statements...');
  connection.query(sqlContent, (err, results) => {
    if (err) {
      console.error('❌ Error executing SQL:', err.message);
      connection.end();
      process.exit(1);
    }
    
    console.log('✅ Database setup completed successfully!');
    
    // Verify by listing tables
    console.log('\n📊 Verifying database tables...');
    connection.query('USE indierepo; SHOW TABLES;', (err, results) => {
      if (err) {
        console.error('❌ Error verifying tables:', err.message);
      } else {
        console.log(`✅ Found ${results[1].length} tables:`);
        results[1].forEach(row => {
          const tableName = Object.values(row)[0];
          console.log(`   - ${tableName}`);
        });
      }
      
      connection.end();
      console.log('\n✨ Database setup complete!');
    });
  });
}); 