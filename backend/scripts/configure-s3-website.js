#!/usr/bin/env node

/**
 * Configure S3 Bucket for Static Website Hosting
 * 
 * This enables accessing files via https://indierepo.com/path/to/file
 * instead of https://indierepo.com.s3.us-east-1.amazonaws.com/path/to/file
 */

const { S3Client, PutBucketWebsiteCommand, GetBucketWebsiteCommand, PutBucketPolicyCommand } = require('@aws-sdk/client-s3');

// Load environment
require('dotenv').config({ path: '.env.production' });

const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME;

async function configureS3Website() {
  console.log('🌐 Configuring S3 Bucket for Static Website Hosting');
  console.log('================================================\n');
  
  try {
    // 1. Configure website hosting
    console.log('⚙️ Setting up website configuration...');
    
    const websiteConfig = {
      Bucket: BUCKET_NAME,
      WebsiteConfiguration: {
        IndexDocument: {
          Suffix: 'index.html'
        },
        ErrorDocument: {
          Key: 'error.html'
        }
      }
    };
    
    const websiteCommand = new PutBucketWebsiteCommand(websiteConfig);
    await s3Client.send(websiteCommand);
    
    console.log('✅ Website configuration applied');
    
    // 2. Set public read policy for website access
    console.log('🔓 Setting up public read policy...');
    
    const bucketPolicy = {
      Version: '2012-10-17',
      Statement: [
        {
          Sid: 'PublicReadGetObject',
          Effect: 'Allow',
          Principal: '*',
          Action: 's3:GetObject',
          Resource: `arn:aws:s3:::${BUCKET_NAME}/*`
        }
      ]
    };
    
    const policyCommand = new PutBucketPolicyCommand({
      Bucket: BUCKET_NAME,
      Policy: JSON.stringify(bucketPolicy)
    });
    
    await s3Client.send(policyCommand);
    console.log('✅ Public read policy applied');
    
    // 3. Get website endpoint
    const getWebsiteCommand = new GetBucketWebsiteCommand({ Bucket: BUCKET_NAME });
    const websiteResult = await s3Client.send(getWebsiteCommand);
    
    const websiteEndpoint = `http://${BUCKET_NAME}.s3-website-${process.env.AWS_REGION}.amazonaws.com`;
    
    console.log('\n🎉 S3 Static Website Hosting Configured!');
    console.log('=======================================');
    console.log(`📍 Website Endpoint: ${websiteEndpoint}`);
    console.log(`🔗 Your domain: https://${BUCKET_NAME}`);
    console.log('\n📝 Access Methods:');
    console.log(`✅ Via S3 Website: ${websiteEndpoint}/games/1/images/file.jpg`);
    console.log(`✅ Via Your Domain: https://${BUCKET_NAME}/games/1/images/file.jpg`);
    console.log(`❌ Old S3 API URL: https://${BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/games/1/images/file.jpg`);
    
    console.log('\n⚠️ Important Notes:');
    console.log('1. Make sure your DNS points indierepo.com to the S3 website endpoint');
    console.log('2. Use HTTPS with CloudFlare or AWS CloudFront for SSL');
    console.log('3. Update your UPLOADS_BASE_URL to: https://indierepo.com');
    
    return {
      websiteEndpoint,
      bucketName: BUCKET_NAME,
      configured: true
    };
    
  } catch (error) {
    console.error('❌ Error configuring S3 website:', error);
    
    if (error.name === 'NoSuchBucket') {
      console.log('💡 Tip: Make sure your bucket name is correct: ' + BUCKET_NAME);
    } else if (error.name === 'AccessDenied') {
      console.log('💡 Tip: Check your AWS credentials have S3 permissions');
    }
    
    throw error;
  }
}

if (require.main === module) {
  configureS3Website()
    .then(() => {
      console.log('\n🚀 Configuration complete! Update your environment variables.');
    })
    .catch((error) => {
      console.error('Configuration failed:', error.message);
      process.exit(1);
    });
}

module.exports = { configureS3Website };
