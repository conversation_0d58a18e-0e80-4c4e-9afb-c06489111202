#!/usr/bin/env node

/**
 * Health Check Script for IndieRepo Backend
 * Verifies database connection and migration status
 */

const dotenv = require('dotenv');
const path = require('path');

// Load environment-specific .env file based on NODE_ENV
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env';
console.log(`Health Check: Loading environment from ${envFile}`);
dotenv.config({ path: path.resolve(process.cwd(), envFile) });
const db = require('../config/database');

async function healthCheck() {
  console.log('🔍 IndieRepo Backend Health Check');
  console.log('==================================');
  
  let allHealthy = true;
  
  try {
    // Test database connection
    console.log('📊 Testing database connection...');
    await db.raw('SELECT 1 as test');
    console.log('✅ Database connection: OK');
    
    // Check if migrations table exists
    console.log('📋 Checking migrations table...');
    const migrationTableExists = await db.schema.hasTable('knex_migrations');
    
    if (migrationTableExists) {
      console.log('✅ Migrations table: EXISTS');
      
      // Get migration status
      console.log('🔄 Checking migration status...');
      const migrations = await db('knex_migrations').select('*').orderBy('batch', 'desc');
      
      if (migrations.length > 0) {
        console.log(`✅ Migrations applied: ${migrations.length} migrations found`);
        console.log('📝 Latest migrations:');
        migrations.slice(0, 5).forEach((migration, index) => {
          console.log(`   ${index + 1}. ${migration.name} (batch ${migration.batch})`);
        });
      } else {
        console.log('⚠️  No migrations found in database');
        allHealthy = false;
      }
    } else {
      console.log('❌ Migrations table: NOT FOUND');
      console.log('   This suggests migrations have never been run.');
      allHealthy = false;
    }
    
    // Test basic table existence
    console.log('🗃️  Checking core tables...');
    const coreTables = ['users', 'games', 'reviews'];
    
    for (const table of coreTables) {
      const exists = await db.schema.hasTable(table);
      if (exists) {
        const count = await db(table).count('* as count').first();
        console.log(`✅ Table '${table}': EXISTS (${count.count} records)`);
      } else {
        console.log(`❌ Table '${table}': NOT FOUND`);
        allHealthy = false;
      }
    }
    
  } catch (error) {
    console.log('❌ Health check failed:');
    console.error(error.message);
    allHealthy = false;
  } finally {
    // Close database connection
    await db.destroy();
  }
  
  console.log('==================================');
  if (allHealthy) {
    console.log('🎉 Overall health: HEALTHY');
    process.exit(0);
  } else {
    console.log('⚠️  Overall health: ISSUES DETECTED');
    process.exit(1);
  }
}

// Run health check
healthCheck().catch(error => {
  console.error('Fatal error during health check:', error);
  process.exit(1);
}); 