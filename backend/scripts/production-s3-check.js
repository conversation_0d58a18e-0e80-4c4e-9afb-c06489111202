#!/usr/bin/env node

/**
 * Production S3 Configuration Check
 * 
 * Run this script on your production server to verify S3 configuration
 * Usage: NODE_ENV=production node scripts/production-s3-check.js
 */

const path = require('path');
const fs = require('fs');

// Load environment based on NODE_ENV
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env';
const envPath = path.join(__dirname, '..', envFile);

console.log(`🔍 Loading environment from: ${envFile}`);
console.log(`📁 Full path: ${envPath}`);
console.log(`📄 File exists: ${fs.existsSync(envPath)}`);

if (fs.existsSync(envPath)) {
  require('dotenv').config({ path: envPath });
  console.log('✅ Environment file loaded successfully');
} else {
  console.error('❌ Environment file not found!');
  process.exit(1);
}

async function productionS3Check() {
  console.log('\n🚀 Production S3 Configuration Check');
  console.log('====================================\n');
  
  // 1. Environment Variables Check
  console.log('📋 Environment Variables:');
  const requiredVars = {
    'NODE_ENV': process.env.NODE_ENV,
    'AWS_REGION': process.env.AWS_REGION,
    'AWS_ACCESS_KEY_ID': process.env.AWS_ACCESS_KEY_ID,
    'AWS_SECRET_ACCESS_KEY': process.env.AWS_SECRET_ACCESS_KEY,
    'AWS_S3_BUCKET_NAME': process.env.AWS_S3_BUCKET_NAME,
    'STORAGE_TYPE': process.env.STORAGE_TYPE,
    'UPLOADS_BASE_URL': process.env.UPLOADS_BASE_URL
  };
  
  let missingVars = [];
  
  Object.entries(requiredVars).forEach(([key, value]) => {
    if (value) {
      // Mask sensitive values
      const displayValue = key.includes('SECRET') || key.includes('KEY') 
        ? `${value.substring(0, 4)}****${value.substring(value.length - 4)}`
        : value;
      console.log(`✅ ${key}: ${displayValue}`);
    } else {
      console.log(`❌ ${key}: NOT SET`);
      missingVars.push(key);
    }
  });
  
  if (missingVars.length > 0) {
    console.log(`\n❌ Missing environment variables: ${missingVars.join(', ')}`);
    return false;
  }
  
  // 2. Expected Configuration Check
  console.log('\n🎯 Expected Configuration:');
  console.log(`Expected bucket name: indierepo`);
  console.log(`Actual bucket name: ${process.env.AWS_S3_BUCKET_NAME}`);
  console.log(`Bucket name matches: ${process.env.AWS_S3_BUCKET_NAME === 'indierepo' ? '✅' : '❌'}`);
  
  console.log(`Expected region: us-east-1`);
  console.log(`Actual region: ${process.env.AWS_REGION}`);
  console.log(`Region matches: ${process.env.AWS_REGION === 'us-east-1' ? '✅' : '❌'}`);
  
  // 3. S3 Service Test
  console.log('\n🔗 Testing S3 Service...');
  try {
    const { checkS3Configuration } = require('../services/s3Service');
    const isS3Ready = await checkS3Configuration();
    
    if (isS3Ready) {
      console.log('✅ S3 service is working correctly!');
      console.log('\n🎉 All checks passed! S3 configuration is ready for production.');
      return true;
    } else {
      console.log('❌ S3 service check failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Error testing S3 service:', error.message);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

// Run the check
if (require.main === module) {
  productionS3Check()
    .then(success => {
      console.log('\n' + '='.repeat(50));
      if (success) {
        console.log('🎉 Production S3 check completed successfully!');
        console.log('Your S3 configuration is ready for game uploads.');
      } else {
        console.log('❌ Production S3 check failed!');
        console.log('Please fix the issues above before deploying.');
      }
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { productionS3Check };
