/**
 * Migration script to upload existing local files to AWS S3
 * 
 * This script helps migrate from local file storage to AWS S3.
 * Run this after setting up S3 and before switching to the new upload system.
 * 
 * Usage: node scripts/migrate_local_to_s3.js
 */

const fs = require('fs').promises;
const path = require('path');
const mysql = require('mysql2/promise');
const { uploadGameFileToS3 } = require('../services/s3Service');

// Database configuration (you may need to adjust these)
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'indierepo'
};

/**
 * Migrate game files to S3
 */
async function migrateGameFiles() {
  const connection = await mysql.createConnection(dbConfig);
  
  try {
    console.log('🚀 Starting game files migration to S3...');
    
    // Get all game files from database
    const [gameFiles] = await connection.execute(`
      SELECT id, gameId, fileName, filePath, fileSize 
      FROM GameFiles 
      WHERE filePath IS NOT NULL 
      AND filePath LIKE '/uploads/%'
      AND s3Key IS NULL
    `);
    
    console.log(`📁 Found ${gameFiles.length} game files to migrate`);
    
    for (const file of gameFiles) {
      try {
        const localPath = path.join(__dirname, '../..', file.filePath);
        
        // Check if local file exists
        try {
          await fs.access(localPath);
        } catch (error) {
          console.warn(`⚠️  Local file not found: ${localPath}`);
          continue;
        }
        
        // Read file and upload to S3
        const fileBuffer = await fs.readFile(localPath);
        const s3Result = await uploadGameFileToS3(
          fileBuffer,
          file.fileName,
          'application/octet-stream', // Default MIME type for game files
          file.gameId,
          'game'
        );
        
        // Update database with S3 information
        await connection.execute(`
          UPDATE GameFiles 
          SET filePath = ?, s3Key = ? 
          WHERE id = ?
        `, [s3Result.url, s3Result.key, file.id]);
        
        console.log(`✅ Migrated: ${file.fileName} (Game ${file.gameId})`);
        
      } catch (error) {
        console.error(`❌ Failed to migrate ${file.fileName}:`, error.message);
      }
    }
    
  } catch (error) {
    console.error('Error migrating game files:', error);
  } finally {
    await connection.end();
  }
}

/**
 * Migrate game images to S3
 */
async function migrateGameImages() {
  const connection = await mysql.createConnection(dbConfig);
  
  try {
    console.log('🖼️  Starting game images migration to S3...');
    
    // Get all game images from database
    const [gameImages] = await connection.execute(`
      SELECT id, gameId, imageType, fileName, filePath, fileSize 
      FROM GameImages 
      WHERE filePath IS NOT NULL 
      AND filePath LIKE '/uploads/%'
      AND s3Key IS NULL
    `);
    
    console.log(`🖼️  Found ${gameImages.length} game images to migrate`);
    
    for (const image of gameImages) {
      try {
        const localPath = path.join(__dirname, '../..', image.filePath);
        
        // Check if local file exists
        try {
          await fs.access(localPath);
        } catch (error) {
          console.warn(`⚠️  Local image not found: ${localPath}`);
          continue;
        }
        
        // Determine MIME type based on file extension
        const ext = path.extname(image.fileName).toLowerCase();
        let mimeType = 'image/jpeg';
        if (ext === '.png') mimeType = 'image/png';
        else if (ext === '.gif') mimeType = 'image/gif';
        
        // Read file and upload to S3
        const fileBuffer = await fs.readFile(localPath);
        const s3Result = await uploadGameFileToS3(
          fileBuffer,
          image.fileName,
          mimeType,
          image.gameId,
          image.imageType
        );
        
        // Update database with S3 information
        await connection.execute(`
          UPDATE GameImages 
          SET filePath = ?, s3Key = ? 
          WHERE id = ?
        `, [s3Result.url, s3Result.key, image.id]);
        
        console.log(`✅ Migrated: ${image.fileName} (${image.imageType}) for Game ${image.gameId}`);
        
      } catch (error) {
        console.error(`❌ Failed to migrate ${image.fileName}:`, error.message);
      }
    }
    
  } catch (error) {
    console.error('Error migrating game images:', error);
  } finally {
    await connection.end();
  }
}

/**
 * Migrate user avatars to S3
 */
async function migrateUserAvatars() {
  const connection = await mysql.createConnection(dbConfig);
  
  try {
    console.log('👤 Starting user avatars migration to S3...');
    
    // Get all user avatars from database
    const [avatars] = await connection.execute(`
      SELECT id, userId, fileName, filePath, fileSize 
      FROM UserAvatars 
      WHERE filePath IS NOT NULL 
      AND filePath LIKE '/uploads/%'
      AND s3Key IS NULL
    `);
    
    console.log(`👤 Found ${avatars.length} user avatars to migrate`);
    
    for (const avatar of avatars) {
      try {
        const localPath = path.join(__dirname, '../..', avatar.filePath);
        
        // Check if local file exists
        try {
          await fs.access(localPath);
        } catch (error) {
          console.warn(`⚠️  Local avatar not found: ${localPath}`);
          continue;
        }
        
        // Determine MIME type based on file extension
        const ext = path.extname(avatar.fileName).toLowerCase();
        let mimeType = 'image/jpeg';
        if (ext === '.png') mimeType = 'image/png';
        
        // Read file and upload to S3
        const fileBuffer = await fs.readFile(localPath);
        const s3Result = await uploadGameFileToS3(
          fileBuffer,
          avatar.fileName,
          mimeType,
          `users/${avatar.userId}`,
          'avatar'
        );
        
        // Update database with S3 information
        await connection.execute(`
          UPDATE UserAvatars 
          SET filePath = ?, s3Key = ? 
          WHERE id = ?
        `, [s3Result.url, s3Result.key, avatar.id]);
        
        console.log(`✅ Migrated: ${avatar.fileName} for User ${avatar.userId}`);
        
      } catch (error) {
        console.error(`❌ Failed to migrate ${avatar.fileName}:`, error.message);
      }
    }
    
  } catch (error) {
    console.error('Error migrating user avatars:', error);
  } finally {
    await connection.end();
  }
}

/**
 * Main migration function
 */
async function main() {
  console.log('🔄 Starting migration from local storage to AWS S3');
  console.log('⚠️  Make sure you have:');
  console.log('   - Configured AWS S3 credentials in .env');
  console.log('   - Run the database migration (add_s3_columns.sql)');
  console.log('   - Backed up your local files and database');
  console.log('');
  
  // Check if S3 is configured
  try {
    const { checkS3Configuration } = require('../services/s3Service');
    const isS3Ready = await checkS3Configuration();
    
    if (!isS3Ready) {
      console.error('❌ S3 is not properly configured. Please check your .env file.');
      process.exit(1);
    }
    
    console.log('✅ S3 configuration verified');
  } catch (error) {
    console.error('❌ Error checking S3 configuration:', error.message);
    process.exit(1);
  }
  
  try {
    // Migrate all file types
    await migrateGameFiles();
    await migrateGameImages();
    await migrateUserAvatars();
    
    console.log('');
    console.log('🎉 Migration completed successfully!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Test that all files are accessible via S3 URLs');
    console.log('2. Verify the application works with S3');
    console.log('3. Consider removing local files after verification');
    console.log('4. Update your application to use the new S3 upload system');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  migrateGameFiles,
  migrateGameImages,
  migrateUserAvatars
}; 