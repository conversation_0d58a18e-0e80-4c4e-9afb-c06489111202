#!/usr/bin/env node

/**
 * S3 Bucket Setup Script
 * 
 * This script helps create and configure the S3 bucket for the application.
 * Run this script to set up the bucket with the correct permissions.
 */

require('dotenv').config();
const { S3Client, CreateBucketCommand, PutBucketPolicyCommand, PutBucketCorsCommand } = require('@aws-sdk/client-s3');

const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME || 'indierepo-games';
const REGION = process.env.AWS_REGION || 'us-east-1';

const s3Client = new S3Client({
  region: REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

async function createBucket() {
  try {
    console.log(`🪣 Creating S3 bucket: ${BUCKET_NAME} in region: ${REGION}`);
    
    const createParams = {
      Bucket: BUCKET_NAME,
    };
    
    // Only add LocationConstraint if not us-east-1 (default region)
    if (REGION !== 'us-east-1') {
      createParams.CreateBucketConfiguration = {
        LocationConstraint: REGION
      };
    }
    
    await s3Client.send(new CreateBucketCommand(createParams));
    console.log('✅ Bucket created successfully');
    return true;
  } catch (error) {
    if (error.name === 'BucketAlreadyOwnedByYou') {
      console.log('✅ Bucket already exists and is owned by you');
      return true;
    } else if (error.name === 'BucketAlreadyExists') {
      console.log('❌ Bucket name already exists (owned by someone else)');
      console.log('💡 Please choose a different bucket name in your environment variables');
      return false;
    } else {
      console.error('❌ Error creating bucket:', error.message);
      return false;
    }
  }
}

async function setBucketPolicy() {
  try {
    console.log('🔐 Setting bucket policy for public read access...');
    
    const bucketPolicy = {
      Version: '2012-10-17',
      Statement: [
        {
          Sid: 'PublicReadGetObject',
          Effect: 'Allow',
          Principal: '*',
          Action: 's3:GetObject',
          Resource: `arn:aws:s3:::${BUCKET_NAME}/*`
        }
      ]
    };
    
    await s3Client.send(new PutBucketPolicyCommand({
      Bucket: BUCKET_NAME,
      Policy: JSON.stringify(bucketPolicy)
    }));
    
    console.log('✅ Bucket policy set successfully');
    return true;
  } catch (error) {
    console.error('❌ Error setting bucket policy:', error.message);
    return false;
  }
}

async function setBucketCors() {
  try {
    console.log('🌐 Setting CORS configuration...');
    
    const corsConfiguration = {
      CORSRules: [
        {
          AllowedHeaders: ['*'],
          AllowedMethods: ['GET', 'PUT', 'POST', 'DELETE', 'HEAD'],
          AllowedOrigins: [
            'https://indierepo.com',
            'http://localhost:5173',
            'http://localhost:3000'
          ],
          ExposeHeaders: ['ETag'],
          MaxAgeSeconds: 3000
        }
      ]
    };
    
    await s3Client.send(new PutBucketCorsCommand({
      Bucket: BUCKET_NAME,
      CORSConfiguration: corsConfiguration
    }));
    
    console.log('✅ CORS configuration set successfully');
    return true;
  } catch (error) {
    console.error('❌ Error setting CORS configuration:', error.message);
    return false;
  }
}

async function setupS3Bucket() {
  console.log('🚀 Setting up S3 bucket for IndieRepo...\n');
  
  // Validate environment variables
  if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
    console.error('❌ AWS credentials not found in environment variables');
    console.log('💡 Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY');
    return false;
  }
  
  console.log(`📋 Configuration:`);
  console.log(`   Bucket Name: ${BUCKET_NAME}`);
  console.log(`   Region: ${REGION}`);
  console.log(`   Access Key: ${process.env.AWS_ACCESS_KEY_ID.substring(0, 4)}****\n`);
  
  try {
    // Step 1: Create bucket
    const bucketCreated = await createBucket();
    if (!bucketCreated) {
      return false;
    }
    
    // Step 2: Set bucket policy
    const policySet = await setBucketPolicy();
    if (!policySet) {
      console.log('⚠️ Bucket created but policy setup failed');
    }
    
    // Step 3: Set CORS configuration
    const corsSet = await setBucketCors();
    if (!corsSet) {
      console.log('⚠️ CORS configuration failed');
    }
    
    console.log('\n🎉 S3 bucket setup completed!');
    console.log('\n📝 Next steps:');
    console.log('1. Test the configuration with: node scripts/test-s3-config.js');
    console.log('2. Try uploading a game to verify everything works');
    console.log('3. Check that files are accessible via the public URLs');
    
    return true;
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    return false;
  }
}

// Run the setup
if (require.main === module) {
  setupS3Bucket()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { setupS3Bucket };
