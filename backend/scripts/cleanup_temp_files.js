#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');

/**
 * Clean up temporary files script
 * This script removes all files in the uploads/temp directory
 */
async function cleanupTempFiles() {
  try {
    const tempDir = path.join(__dirname, '../../uploads/temp');
    
    console.log('🧹 Starting cleanup of temporary files...');
    console.log(`📁 Temp directory: ${tempDir}`);
    
    // Check if temp directory exists
    try {
      await fs.access(tempDir);
    } catch (error) {
      console.log('✅ Temp directory does not exist or is empty');
      return;
    }
    
    const items = await fs.readdir(tempDir);
    
    if (items.length === 0) {
      console.log('✅ Temp directory is already clean');
      return;
    }
    
    console.log(`📋 Found ${items.length} items to clean up`);
    
    let filesDeleted = 0;
    let dirsDeleted = 0;
    let errors = 0;
    
    for (const item of items) {
      const itemPath = path.join(tempDir, item);
      
      try {
        const stats = await fs.stat(itemPath);
        
        if (stats.isFile()) {
          await fs.unlink(itemPath);
          filesDeleted++;
          console.log(`🗑️  Deleted file: ${item}`);
        } else if (stats.isDirectory()) {
          await fs.rmdir(itemPath, { recursive: true });
          dirsDeleted++;
          console.log(`📁 Deleted directory: ${item}`);
        }
      } catch (error) {
        errors++;
        console.error(`❌ Error deleting ${item}:`, error.message);
      }
    }
    
    console.log('\n📊 Cleanup Summary:');
    console.log(`✅ Files deleted: ${filesDeleted}`);
    console.log(`📁 Directories deleted: ${dirsDeleted}`);
    console.log(`❌ Errors: ${errors}`);
    console.log(`🎉 Cleanup completed!`);
    
  } catch (error) {
    console.error('💥 Fatal error during cleanup:', error);
    process.exit(1);
  }
}

// Run cleanup if this script is executed directly
if (require.main === module) {
  cleanupTempFiles()
    .then(() => {
      console.log('\n🏁 Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = { cleanupTempFiles }; 