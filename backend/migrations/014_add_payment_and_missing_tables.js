/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function(knex) {
  // Add payment fields to games table
  const gamesExists = await knex.schema.hasTable('games');
  if (gamesExists) {
    const hasPaymentEnabled = await knex.schema.hasColumn('games', 'payment_enabled');
    if (!hasPaymentEnabled) {
      await knex.schema.table('games', (table) => {
        table.boolean('payment_enabled').defaultTo(false);
        table.string('paypal_client_id', 255).nullable();
        table.string('google_pay_client_id', 255).nullable();
        table.string('stripe_publishable_key', 255).nullable();
        table.json('payment_methods_enabled').nullable();
        table.boolean('revenue_sharing_enabled').defaultTo(false);
        table.decimal('revenue_share_percentage', 5, 2).defaultTo(0);
      });
    }
  }

  // Add referred_by to users table
  const usersExists = await knex.schema.hasTable('users');
  if (usersExists) {
    const hasReferredBy = await knex.schema.hasColumn('users', 'referred_by');
    if (!hasReferredBy) {
      await knex.schema.table('users', (table) => {
        table.integer('referred_by').nullable();
        table.foreign('referred_by').references('id').inTable('users').onDelete('SET NULL');
      });
    }
  }

  // Create Purchases table
  const purchasesExists = await knex.schema.hasTable('purchases');
  if (!purchasesExists) {
    await knex.schema.createTable('purchases', (table) => {
      table.increments('id').primary();
      table.integer('game_id').unsigned().notNullable();
      table.integer('user_id').unsigned().notNullable();
      table.string('transaction_id', 255).notNullable().unique();
      table.string('payment_method', 20).notNullable();
      table.decimal('amount', 10, 2).notNullable();
      table.string('currency', 10).defaultTo('USD');
      table.string('status', 20).defaultTo('pending');
      table.timestamp('purchase_date').defaultTo(knex.fn.now());
      table.timestamp('refund_date').nullable();
      
      table.foreign('game_id').references('id').inTable('games').onDelete('CASCADE');
      table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    });
  }

  // Create User_Games table
  const userGamesExists = await knex.schema.hasTable('user_games');
  if (!userGamesExists) {
    await knex.schema.createTable('user_games', (table) => {
      table.integer('user_id').unsigned().notNullable();
      table.integer('game_id').unsigned().notNullable();
      table.boolean('is_favorite').defaultTo(false);
      table.boolean('in_library').defaultTo(false);
      table.string('purchase_type', 20).nullable();
      table.timestamps(true, true);
      
      table.primary(['user_id', 'game_id']);
      table.foreign('user_id').references('id').inTable('users');
      table.foreign('game_id').references('id').inTable('games');
    });
  }

  // Create System Config table
  const systemConfigExists = await knex.schema.hasTable('system_config');
  if (!systemConfigExists) {
    await knex.schema.createTable('system_config', (table) => {
      table.increments('id').primary();
      table.string('config_key', 100).notNullable().unique();
      table.text('config_value').nullable();
      table.text('description').nullable();
      table.timestamps(true, true);
    });

    // Insert initial system configuration
    await knex('system_config').insert([
      { config_key: 'db_version', config_value: '1.0', description: 'Current database schema version' },
      { config_key: 's3_migration_status', config_value: 'not_started', description: 'Status of S3 migration (not_started, in_progress, completed)' },
      { config_key: 'storage_backend', config_value: 'local', description: 'Current storage backend (local, s3, hybrid)' }
    ]);
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
  // Drop tables
  await knex.schema.dropTableIfExists('system_config');
  await knex.schema.dropTableIfExists('user_games');
  await knex.schema.dropTableIfExists('purchases');

  // Remove payment fields from games
  const gamesExists = await knex.schema.hasTable('games');
  if (gamesExists) {
    await knex.schema.table('games', (table) => {
      const columnsToRemove = [
        'payment_enabled', 'paypal_client_id', 'google_pay_client_id',
        'stripe_publishable_key', 'payment_methods_enabled',
        'revenue_sharing_enabled', 'revenue_share_percentage'
      ];
      
      columnsToRemove.forEach(column => {
        try {
          table.dropColumn(column);
        } catch (e) {
          // Column might not exist, ignore
        }
      });
    });
  }

  // Remove referred_by from users
  const usersExists = await knex.schema.hasTable('users');
  if (usersExists) {
    await knex.schema.table('users', (table) => {
      try {
        table.dropColumn('referred_by');
      } catch (e) {
        // Column might not exist, ignore
      }
    });
  }
}; 