/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('users', (table) => {
    table.increments('id').primary();
    table.string('username', 30).notNullable().unique();
    table.string('email', 100).notNullable().unique();
    table.string('password', 100).nullable(); // Allow null for social login users
    table.string('google_id', 255).nullable().unique();
    table.string('discord_id', 255).nullable().unique();
    table.string('profile_image', 255).nullable();
    table.string('display_name', 100).nullable();
    table.boolean('is_google_user').defaultTo(false);
    table.boolean('is_verified').defaultTo(false);
    table.string('provider', 20).defaultTo('local');
    table.string('role', 20).defaultTo('user');
    table.integer('credits').defaultTo(0);
    table.string('referral_code', 20).nullable().unique();
    table.timestamps(true, true); // created_at and updated_at with default values
    
    // Add indexes for better performance
    table.index(['email']);
    table.index(['username']);
    table.index(['google_id']);
    table.index(['discord_id']);
    table.index(['referral_code']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('users');
}; 