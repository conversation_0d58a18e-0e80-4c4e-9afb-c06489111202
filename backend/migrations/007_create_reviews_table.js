/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('reviews', (table) => {
    table.increments('id').primary();
    table.integer('user_id').unsigned().notNullable();
    table.integer('game_id').unsigned().notNullable();
    table.string('title', 255);
    table.text('comment');
    table.integer('rating').notNullable(); // 1-5 star rating
    table.integer('likes_count').defaultTo(0);
    table.integer('dislikes_count').defaultTo(0);
    table.integer('comment_count').defaultTo(0);
    table.boolean('is_verified_purchase').defaultTo(false);
    table.timestamps(true, true);

    // Foreign key constraints
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.foreign('game_id').references('id').inTable('games').onDelete('CASCADE');
    
    // Ensure one review per user per game
    table.unique(['user_id', 'game_id']);
    
    // Add indexes for better performance
    table.index(['game_id']);
    table.index(['user_id']);
    table.index(['rating']);
    table.index(['created_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('reviews');
}; 