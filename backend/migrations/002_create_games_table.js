/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('games', (table) => {
    table.increments('id').primary();
    table.integer('user_id').unsigned().notNullable();
    table.string('title', 255).notNullable();
    table.text('description');
    table.string('genre', 100);
    table.text('tags'); // JSON or comma-separated tags
    table.decimal('price', 10, 2).defaultTo(0.00);
    table.boolean('is_free').defaultTo(true);
    table.string('status', 50).defaultTo('published'); // draft, published, removed
    table.integer('view_count').defaultTo(0);
    table.integer('download_count').defaultTo(0);
    table.integer('likes_count').defaultTo(0);
    table.float('average_rating').defaultTo(0);
    table.integer('rating_count').defaultTo(0);
    table.string('file_path', 500); // Path to game files
    table.bigInteger('file_size'); // File size in bytes
    table.string('version', 50).defaultTo('1.0.0');
    table.text('changelog');
    table.string('platform', 100).defaultTo('web'); // web, windows, mac, linux, android, ios
    table.timestamps(true, true);

    // Foreign key constraint
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    // Add indexes for better performance
    table.index(['user_id']);
    table.index(['genre']);
    table.index(['status']);
    table.index(['is_free']);
    table.index(['created_at']);
    table.index(['view_count']);
    table.index(['download_count']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('games');
}; 