/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('review_comments', (table) => {
    table.increments('id').primary();
    table.integer('review_id').unsigned().notNullable();
    table.integer('user_id').unsigned().notNullable();
    table.text('content').notNullable();
    table.integer('parent_id').unsigned().nullable(); // For nested comments
    table.timestamps(true, true);

    // Foreign key constraints
    table.foreign('review_id').references('id').inTable('reviews').onDelete('CASCADE');
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.foreign('parent_id').references('id').inTable('review_comments').onDelete('CASCADE');
    
    // Add indexes for better performance
    table.index(['review_id']);
    table.index(['user_id']);
    table.index(['parent_id']);
    table.index(['created_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('review_comments');
}; 