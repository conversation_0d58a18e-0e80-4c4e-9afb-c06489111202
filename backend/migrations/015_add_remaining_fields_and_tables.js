/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function(knex) {
  // Add missing fields to game_files table
  const gameFilesExists = await knex.schema.hasTable('game_files');
  if (gameFilesExists) {
    const hasDescription = await knex.schema.hasColumn('game_files', 'description');
    if (!hasDescription) {
      await knex.schema.table('game_files', (table) => {
        table.string('description', 255).defaultTo('');
        table.boolean('requires_purchase').defaultTo(false);
        table.boolean('is_external_link').defaultTo(false);
        table.string('external_url', 255).defaultTo('');
        table.boolean('is_web_playable').defaultTo(false);
        table.string('web_entry_point', 255).defaultTo('');
        table.string('s3_key', 500).nullable();
        table.datetime('uploaded_at').defaultTo(knex.fn.now());
      });
    }
  }

  // Add S3 keys to existing tables
  const gameImagesExists = await knex.schema.hasTable('game_images');
  if (gameImagesExists) {
    const hasS3Key = await knex.schema.hasColumn('game_images', 's3_key');
    if (!hasS3Key) {
      await knex.schema.table('game_images', (table) => {
        table.string('s3_key', 500).nullable();
      });
    }
  }

  const gameScreenshotsExists = await knex.schema.hasTable('game_screenshots');
  if (gameScreenshotsExists) {
    const hasS3Key = await knex.schema.hasColumn('game_screenshots', 's3_key');
    if (!hasS3Key) {
      await knex.schema.table('game_screenshots', (table) => {
        table.string('s3_key', 500).nullable();
      });
    }
  }

  const userAvatarsExists = await knex.schema.hasTable('user_avatars');
  if (userAvatarsExists) {
    const hasS3Key = await knex.schema.hasColumn('user_avatars', 's3_key');
    if (!hasS3Key) {
      await knex.schema.table('user_avatars', (table) => {
        table.string('s3_key', 500).nullable();
      });
    }
  }

  // Create Credit transactions table
  const creditTransactionsExists = await knex.schema.hasTable('credit_transactions');
  if (!creditTransactionsExists) {
    await knex.schema.createTable('credit_transactions', (table) => {
      table.increments('id').primary();
      table.integer('user_id').unsigned().notNullable();
      table.integer('amount').notNullable();
      table.string('description', 255).nullable();
      table.string('transaction_type', 20).notNullable();
      table.integer('game_id').unsigned().nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      
      table.foreign('user_id').references('id').inTable('users');
      table.foreign('game_id').references('id').inTable('games');
    });
  }

  // Create Referral transactions table
  const referralTransactionsExists = await knex.schema.hasTable('referral_transactions');
  if (!referralTransactionsExists) {
    await knex.schema.createTable('referral_transactions', (table) => {
      table.increments('id').primary();
      table.integer('referrer_id').unsigned().notNullable();
      table.integer('referred_id').unsigned().notNullable();
      table.integer('credits_awarded').defaultTo(0);
      table.string('status', 20).defaultTo('pending');
      table.timestamps(true, true);
      
      table.foreign('referrer_id').references('id').inTable('users');
      table.foreign('referred_id').references('id').inTable('users');
    });
  }

  // Create Payment History table
  const paymentHistoryExists = await knex.schema.hasTable('payment_history');
  if (!paymentHistoryExists) {
    await knex.schema.createTable('payment_history', (table) => {
      table.increments('id').primary();
      table.integer('purchase_id').unsigned().nullable();
      table.json('raw_payment_data').nullable();
      table.string('payment_provider', 20).notNullable();
      table.string('payment_provider_id', 255).nullable();
      table.json('processing_details').nullable();
      table.text('error_message').nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      
      table.foreign('purchase_id').references('id').inTable('purchases').onDelete('SET NULL');
    });
  }

  // Create Payout Requests table
  const payoutRequestsExists = await knex.schema.hasTable('payout_requests');
  if (!payoutRequestsExists) {
    await knex.schema.createTable('payout_requests', (table) => {
      table.increments('id').primary();
      table.integer('user_id').unsigned().notNullable();
      table.decimal('amount', 10, 2).notNullable();
      table.string('currency', 10).defaultTo('USD');
      table.string('status', 20).defaultTo('pending');
      table.string('payment_method', 20).notNullable();
      table.json('payment_details').nullable();
      table.timestamp('request_date').defaultTo(knex.fn.now());
      table.timestamp('processed_date').nullable();
      
      table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    });
  }

  // Create User Payment Settings table
  const userPaymentSettingsExists = await knex.schema.hasTable('user_payment_settings');
  if (!userPaymentSettingsExists) {
    await knex.schema.createTable('user_payment_settings', (table) => {
      table.integer('user_id').unsigned().primary();
      table.string('paypal_email', 255).nullable();
      table.string('paypal_client_id', 255).nullable();
      table.string('google_pay_client_id', 255).nullable();
      table.string('stripe_publishable_key', 255).nullable();
      table.json('bank_account_info').nullable();
      table.string('preferred_payout_method', 20).defaultTo('paypal');
      table.decimal('minimum_payout_amount', 10, 2).defaultTo(20.00);
      table.boolean('auto_payout_enabled').defaultTo(false);
      table.timestamp('last_updated').defaultTo(knex.fn.now());
      
      table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
  // Drop new tables
  await knex.schema.dropTableIfExists('user_payment_settings');
  await knex.schema.dropTableIfExists('payout_requests');
  await knex.schema.dropTableIfExists('payment_history');
  await knex.schema.dropTableIfExists('credit_transactions');
  await knex.schema.dropTableIfExists('referral_transactions');

  // Remove added columns from existing tables
  const gameFilesExists = await knex.schema.hasTable('game_files');
  if (gameFilesExists) {
    await knex.schema.table('game_files', (table) => {
      const columnsToRemove = [
        'description', 'requires_purchase', 'is_external_link', 'external_url',
        'is_web_playable', 'web_entry_point', 's3_key', 'uploaded_at'
      ];
      
      columnsToRemove.forEach(column => {
        try {
          table.dropColumn(column);
        } catch (e) {
          // Column might not exist, ignore
        }
      });
    });
  }

  // Remove S3 keys from existing tables
  const tableS3Columns = [
    { table: 'game_images', column: 's3_key' },
    { table: 'game_screenshots', column: 's3_key' },
    { table: 'user_avatars', column: 's3_key' }
  ];

  for (const { table: tableName, column } of tableS3Columns) {
    const tableExists = await knex.schema.hasTable(tableName);
    if (tableExists) {
      await knex.schema.table(tableName, (table) => {
        try {
          table.dropColumn(column);
        } catch (e) {
          // Column might not exist, ignore
        }
      });
    }
  }
}; 