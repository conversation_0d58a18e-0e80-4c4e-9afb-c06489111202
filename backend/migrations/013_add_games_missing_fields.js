/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function(knex) {
  const gamesExists = await knex.schema.hasTable('games');
  if (gamesExists) {
    // Check each column individually and add if it doesn't exist
    const hasSlug = await knex.schema.hasColumn('games', 'slug');
    if (!hasSlug) {
      await knex.schema.table('games', (table) => {
        table.string('slug', 100).unique().nullable();
      });
    }

    const hasPriceModel = await knex.schema.hasColumn('games', 'price_model');
    if (!hasPriceModel) {
      await knex.schema.table('games', (table) => {
        table.string('price_model', 20).defaultTo('free');
      });
    }

    const hasCreditPrice = await knex.schema.hasColumn('games', 'credit_price');
    if (!hasCreditPrice) {
      await knex.schema.table('games', (table) => {
        table.integer('credit_price').defaultTo(0);
      });
    }

    const hasMainVideoUrl = await knex.schema.hasColumn('games', 'main_video_url');
    if (!hasMainVideoUrl) {
      await knex.schema.table('games', (table) => {
        table.string('main_video_url', 255).nullable();
      });
    }

    const hasSteamUrl = await knex.schema.hasColumn('games', 'steam_url');
    if (!hasSteamUrl) {
      await knex.schema.table('games', (table) => {
        table.string('steam_url', 255).nullable();
      });
    }

    const hasItchUrl = await knex.schema.hasColumn('games', 'itch_url');
    if (!hasItchUrl) {
      await knex.schema.table('games', (table) => {
        table.string('itch_url', 255).nullable();
      });
    }

    const hasEpicGamesUrl = await knex.schema.hasColumn('games', 'epic_games_url');
    if (!hasEpicGamesUrl) {
      await knex.schema.table('games', (table) => {
        table.string('epic_games_url', 255).nullable();
      });
    }

    const hasIsWebGame = await knex.schema.hasColumn('games', 'is_web_game');
    if (!hasIsWebGame) {
      await knex.schema.table('games', (table) => {
        table.boolean('is_web_game').defaultTo(false);
      });
    }

    const hasWebGameUrl = await knex.schema.hasColumn('games', 'web_game_url');
    if (!hasWebGameUrl) {
      await knex.schema.table('games', (table) => {
        table.string('web_game_url', 255).nullable();
      });
    }

    const hasWebGameType = await knex.schema.hasColumn('games', 'web_game_type');
    if (!hasWebGameType) {
      await knex.schema.table('games', (table) => {
        table.string('web_game_type', 20).nullable();
      });
    }

    const hasEmbeddedVersion = await knex.schema.hasColumn('games', 'has_embedded_version');
    if (!hasEmbeddedVersion) {
      await knex.schema.table('games', (table) => {
        table.boolean('has_embedded_version').defaultTo(false);
      });
    }

    const hasReleaseDate = await knex.schema.hasColumn('games', 'release_date');
    if (!hasReleaseDate) {
      await knex.schema.table('games', (table) => {
        table.datetime('release_date').defaultTo(knex.fn.now());
      });
    }
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
  const gamesExists = await knex.schema.hasTable('games');
  if (gamesExists) {
    await knex.schema.table('games', (table) => {
      const columnsToRemove = [
        'slug', 'price_model', 'credit_price', 'main_video_url', 'steam_url',
        'itch_url', 'epic_games_url', 'is_web_game', 'web_game_url', 'web_game_type',
        'has_embedded_version', 'release_date'
      ];
      
      columnsToRemove.forEach(column => {
        try {
          table.dropColumn(column);
        } catch (e) {
          // Column might not exist, ignore
        }
      });
    });
  }
}; 