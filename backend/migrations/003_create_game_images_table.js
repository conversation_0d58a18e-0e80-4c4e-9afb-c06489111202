/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('game_images', (table) => {
    table.increments('id').primary();
    table.integer('game_id').unsigned().notNullable();
    table.string('image_type', 50).notNullable(); // 'cover', 'card', 'gif', 'screenshot'
    table.string('file_path', 500).notNullable();
    table.string('file_name', 255);
    table.integer('file_size');
    table.string('mime_type', 100);
    table.integer('display_order').defaultTo(0);
    table.timestamps(true, true);

    // Foreign key constraint
    table.foreign('game_id').references('id').inTable('games').onDelete('CASCADE');
    
    // Add indexes for better performance
    table.index(['game_id']);
    table.index(['image_type']);
    table.index(['display_order']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('game_images');
}; 