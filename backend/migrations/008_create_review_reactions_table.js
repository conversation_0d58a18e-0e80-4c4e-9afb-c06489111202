/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('review_reactions', (table) => {
    table.increments('id').primary();
    table.integer('user_id').unsigned().notNullable();
    table.integer('review_id').unsigned().notNullable();
    table.string('reaction_type', 20).notNullable(); // 'like', 'dislike'
    table.timestamps(true, true);

    // Foreign key constraints
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.foreign('review_id').references('id').inTable('reviews').onDelete('CASCADE');
    
    // Ensure one reaction per user per review
    table.unique(['user_id', 'review_id']);
    
    // Add indexes for better performance
    table.index(['review_id']);
    table.index(['user_id']);
    table.index(['reaction_type']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('review_reactions');
}; 