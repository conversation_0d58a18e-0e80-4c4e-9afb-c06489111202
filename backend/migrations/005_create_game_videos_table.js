/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('game_videos', (table) => {
    table.increments('id').primary();
    table.integer('game_id').unsigned().notNullable();
    table.string('video_url', 500).notNullable();
    table.string('video_type', 50).defaultTo('trailer'); // 'trailer', 'gameplay', 'demo'
    table.string('title', 255);
    table.text('description');
    table.integer('display_order').defaultTo(0);
    table.timestamps(true, true);

    // Foreign key constraint
    table.foreign('game_id').references('id').inTable('games').onDelete('CASCADE');
    
    // Add indexes for better performance
    table.index(['game_id']);
    table.index(['video_type']);
    table.index(['display_order']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('game_videos');
}; 