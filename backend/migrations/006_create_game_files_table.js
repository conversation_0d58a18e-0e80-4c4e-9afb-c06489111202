/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('game_files', (table) => {
    table.increments('id').primary();
    table.integer('game_id').unsigned().notNullable();
    table.string('file_path', 500).notNullable();
    table.string('file_name', 255).notNullable();
    table.bigInteger('file_size');
    table.string('mime_type', 100);
    table.string('file_type', 50).defaultTo('game'); // 'game', 'documentation', 'source'
    table.string('platform', 100); // 'web', 'windows', 'mac', 'linux', 'android', 'ios'
    table.string('version', 50).defaultTo('1.0.0');
    table.boolean('is_main_file').defaultTo(false);
    table.string('download_url', 500);
    table.text('notes');
    table.timestamps(true, true);

    // Foreign key constraint
    table.foreign('game_id').references('id').inTable('games').onDelete('CASCADE');
    
    // Add indexes for better performance
    table.index(['game_id']);
    table.index(['file_type']);
    table.index(['platform']);
    table.index(['is_main_file']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('game_files');
}; 