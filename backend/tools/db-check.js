require('dotenv').config();
const db = require('../config/database');

async function checkDatabase() {
  try {
    console.log('Checking database connection...');
    
    // Check tables
    const [tables] = await db.query('SHOW TABLES');
    console.log('Available tables:');
    tables.forEach(table => {
      console.log('-', Object.values(table)[0]);
    });
    
    // Check Games table
    console.log('\nChecking Games table...');
    const [games] = await db.query('SELECT COUNT(*) as count FROM Games');
    console.log(`Found ${games[0].count} games in database`);
    
    if (games[0].count > 0) {
      const [gamesSample] = await db.query('SELECT * FROM Games LIMIT 1');
      console.log('Sample game record:', JSON.stringify(gamesSample[0], null, 2));
    }
    
    // Check GameImages table if it exists
    if (tables.some(t => Object.values(t)[0] === 'GameImages')) {
      console.log('\nChecking GameImages table...');
      const [imagesCount] = await db.query('SELECT COUNT(*) as count FROM GameImages');
      console.log(`Found ${imagesCount[0].count} game images in database`);
    }
    
    console.log('\nDatabase check completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error during database check:', error);
    process.exit(1);
  }
}

checkDatabase();
