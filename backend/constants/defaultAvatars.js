/**
 * Default Avatar Constants
 * Contains predefined avatar URLs from AWS S3 bucket
 */

// Base S3 URL for avatar folder (you can change this based on your S3 bucket configuration)
const S3_AVATARS_BASE_URL = process.env.UPLOADS_BASE_URL || 'https://indierepo.s3.us-east-1.amazonaws.com';

/**
 * List of default avatars available in S3
 * These should be uploaded to the S3 bucket in the 'avatar/' folder
 */
const DEFAULT_AVATARS = [
  {
    id: 1,
    filename: '1.png',
    url: `${S3_AVATARS_BASE_URL}/avatar/1.png`,
    name: 'Avatar 1'
  },
  {
    id: 2,
    filename: '2.png',
    url: `${S3_AVATARS_BASE_URL}/avatar/2.png`,
    name: 'Avatar 2'
  }
  // Add more avatars here as you create them
  // {
  //   id: 3,
  //   filename: '3.png',
  //   url: `${S3_AVATARS_BASE_URL}/avatar/3.png`,
  //   name: 'Avatar 3'
  // }
];

/**
 * Get a random default avatar from the available list
 * @returns {Object} Random avatar object with url, filename, and name
 */
const getRandomDefaultAvatar = () => {
  const randomIndex = Math.floor(Math.random() * DEFAULT_AVATARS.length);
  return DEFAULT_AVATARS[randomIndex];
};

/**
 * Get default avatar by ID
 * @param {number} id - Avatar ID
 * @returns {Object|null} Avatar object or null if not found
 */
const getDefaultAvatarById = (id) => {
  return DEFAULT_AVATARS.find(avatar => avatar.id === id) || null;
};

/**
 * Get all available default avatars
 * @returns {Array} Array of all default avatar objects
 */
const getAllDefaultAvatars = () => {
  return [...DEFAULT_AVATARS];
};

module.exports = {
  DEFAULT_AVATARS,
  getRandomDefaultAvatar,
  getDefaultAvatarById,
  getAllDefaultAvatars,
  S3_AVATARS_BASE_URL
}; 