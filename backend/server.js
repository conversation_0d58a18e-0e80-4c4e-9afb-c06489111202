const dotenv = require('dotenv');
const path = require('path');

// Load environment-specific .env file based on NODE_ENV
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env';
console.log(`Loading environment from ${envFile}`);
dotenv.config({ path: path.resolve(process.cwd(), envFile) });
const app = require('./app');
const logger = require('./config/logger');

// Ensure required directories exist
require('./scripts/ensure-directories');

const PORT = process.env.PORT || 3000;

// Start the server
app.listen(PORT, () => {
  logger.info(`
╔════════════════════════════════════════════════════════════╗
║                                                            ║
║         IndieRepo API Server Running on Port ${PORT}          ║
║                                                            ║
╚════════════════════════════════════════════════════════════╝
  `);
});
