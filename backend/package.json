{"name": "team3-backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node --env-file=.env --watch server.js", "migrate": "knex migrate:latest", "migrate:rollback": "knex migrate:rollback", "migrate:make": "knex migrate:make", "migrate:status": "knex migrate:status", "migrate:prod": "NODE_ENV=production knex migrate:latest", "migrate:status:prod": "NODE_ENV=production knex migrate:status", "migrate:rollback:prod": "NODE_ENV=production knex migrate:rollback", "db:init": "node scripts/init-db.js", "db:init:prod": "NODE_ENV=production node scripts/init-db.js", "health-check": "node scripts/health-check.js", "deploy": "npm install --production && npm run db:init:prod && npm run migrate:prod"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.828.0", "@aws-sdk/s3-request-presigner": "^3.828.0", "aws-sdk": "^2.1692.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "google-auth-library": "^9.4.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "knex": "^3.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "pg": "^8.12.0", "sharp": "^0.33.5", "uuid": "^11.1.0", "winston": "^3.17.0"}}