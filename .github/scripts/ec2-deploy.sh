#!/bin/bash
# IndieRepo MERN Application CI/CD Deployment Script
# This is executed on the EC2 instance after GitHub Actions pushes the files

# Exit on error
set -e

# Log output to a file for debugging CI/CD issues
DEPLOY_LOG="/var/log/indierepo-deploy.log"
exec > >(tee -a ${DEPLOY_LOG}) 2>&1

echo "====================================================="
echo "     IndieRepo CI/CD Deployment Script"
echo "     $(date)"
echo "====================================================="

# Configuration
APP_PATH="/var/www/indierepo"
DOMAIN="indierepo.com"
DB_HOST="indierepo.cy544o8qq9ug.us-east-1.rds.amazonaws.com"
DB_USER="postgres"
DB_PASSWORD="JgrK3L2H.UQK%jFu"
DB_NAME="indierepo"

# Create directories if they don't exist
mkdir -p ${APP_PATH}/frontend
mkdir -p ${APP_PATH}/backend
mkdir -p ${APP_PATH}/logs

# Check if this is a first-time deployment
FIRST_DEPLOY=false
if [ ! -f "${APP_PATH}/deployed.flag" ]; then
  FIRST_DEPLOY=true
  echo "First-time deployment detected. Will perform full setup."
fi

# Process deployment files
echo "Processing deployment files from GitHub Actions..."
DEPLOY_SRC="/tmp/deploy"

# Make sure SELinux permissions are correctly set
if command -v getenforce &> /dev/null; then
  if [ "$(getenforce)" != "Disabled" ]; then
    echo "Setting SELinux permissions..."
    chcon -Rt httpd_sys_content_t ${APP_PATH}
  fi
fi

# 1. Copy the backend files
echo "Updating backend files..."
cp -R ${DEPLOY_SRC}/backend/* ${APP_PATH}/backend/

# 2. Copy the frontend files
echo "Updating frontend files..."
rm -rf ${APP_PATH}/frontend/*  # Clean old build
cp -R ${DEPLOY_SRC}/frontend-dist/* ${APP_PATH}/frontend/

# Skip copying createDB.sql as we're now using Knex migrations
echo "Using Knex migrations instead of SQL files for database setup"

# Skip Nginx configuration update as it's already configured manually
echo "Skipping Nginx configuration update as it's already configured manually"

# 4. Update environment files
echo "Updating environment files..."

# Backend production environment
cat > ${APP_PATH}/backend/.env.production << EOF
# Environment Configuration
# Production environment settings for AWS EC2 deployment

# Server Configuration
PORT=5000
NODE_ENV=production

# Database Configuration - AWS RDS PostgreSQL Connection
DB_HOST=${DB_HOST}
DB_USER=${DB_USER}
DB_PASSWORD=${DB_PASSWORD}
DB_NAME=${DB_NAME}
DB_PORT=5432

# JWT Configuration - Use a strong production secret
JWT_SECRET=${JWT_SECRET:-$(openssl rand -hex 32)}
JWT_EXPIRES_IN=24h

# OAuth Configuration
GOOGLE_CLIENT_ID=383267241104-v4a33murq9oc29jh4p92gf7po1svjtal.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
DISCORD_CLIENT_ID=1344353796653842513
DISCORD_CLIENT_SECRET=vjj4plfcwvW1uFeUDxDrQm2WRv_UhhDp
DISCORD_REDIRECT_URI=https://${DOMAIN}/auth/discord/callback

# Domain Configuration
ROOT_DOMAIN=${DOMAIN}
DOMAIN=${DOMAIN}
FRONTEND_URL=https://${DOMAIN}
BACKEND_URL=https://${DOMAIN}/api
APP_PORT=5000

# Debug Flags
DEBUG_UNITY=false

# AWS S3 Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=mW1AUryTFndcpY+6ZaIDWwG/9kGh1irAl2ZNvHAH
AWS_S3_BUCKET_NAME=indierepo

# File Storage Configuration
STORAGE_TYPE=s3
# Use direct S3 URLs for serving files
UPLOADS_BASE_URL=https://indierepo.s3.us-east-1.amazonaws.com

# Enable SSL for RDS connections
DB_SSL=true

EOF

# Copy .env.production to .env for the backend
cp ${APP_PATH}/backend/.env.production ${APP_PATH}/backend/.env

# First-time setup if needed
if $FIRST_DEPLOY; then
  echo "Performing first-time setup..."
  
  # Skip Nginx, Node.js and SSL installation as they're already installed
  echo "Using pre-installed Nginx, Node.js and SSL certificates"
  
  # Skip PM2 installation as it's already installed
  echo "Using pre-installed PM2"
  
  # Install PostgreSQL client
  if ! command -v psql &> /dev/null; then
    echo "PostgreSQL client not found. Installing..."
    # Try multiple PostgreSQL client package names for Amazon Linux 2023
    if dnf list postgresql15 &> /dev/null; then
      echo "Installing postgresql15 package..."
      dnf install -y postgresql15
    elif dnf list postgresql &> /dev/null; then
      echo "Installing postgresql package..."
      dnf install -y postgresql
    else
      echo "ERROR: Could not find PostgreSQL client package."
      echo "Please manually install postgresql client before continuing."
      exit 1
    fi
  fi
  
  # Test connection to AWS RDS PostgreSQL
  echo "Testing connection to AWS RDS PostgreSQL..."
  
  # Export PostgreSQL connection variables for psql
  export PGHOST=${DB_HOST}
  export PGPORT=5432
  export PGUSER=${DB_USER}
  export PGPASSWORD=${DB_PASSWORD}
  
  # First connect to default postgres database
  export PGDATABASE="postgres"
  
  echo "Connecting to PostgreSQL server..."
  if psql -c "SELECT version();" 2>/dev/null; then
    echo "✓ Successfully connected to RDS PostgreSQL server"
    echo "PostgreSQL version:"
    psql -c "SELECT version();" -t
    
    # Check if our database exists
    echo "Checking if database '${DB_NAME}' exists..."
    DB_EXISTS=$(psql -t -c "SELECT 1 FROM pg_database WHERE datname='${DB_NAME}';" | grep -c 1)
    
    if [ "$DB_EXISTS" -eq "1" ]; then
      echo "✓ Database '${DB_NAME}' exists"
    else
      echo "Database '${DB_NAME}' does not exist. Creating it now..."
      if psql -c "CREATE DATABASE ${DB_NAME};" 2>/dev/null; then
        echo "✓ Successfully created database '${DB_NAME}'"
      else
        echo "❌ Failed to create database '${DB_NAME}'. Check permissions."
        # Continue with deployment, migrations will fail but we'll at least deploy code
      fi
    fi
    
    # Switch to our database to test connection
    export PGDATABASE=${DB_NAME}
    if psql -c "SELECT 1;" 2>/dev/null; then
      echo "✓ Connection to database '${DB_NAME}' successful"
    else
      echo "❌ Could not connect to database '${DB_NAME}' after creation"
    fi
    
    # Show all databases
    export PGDATABASE="postgres"
    echo "Databases available:"
    psql -c "\l" -t
    
    echo "✓ Database connection verified!"
  else
    echo "⚠️  Warning: Could not connect to RDS PostgreSQL database"
    echo "Please verify:"
    echo " - Database credentials are correct"
    echo " - EC2 security group allows outbound to port 5432"
    echo " - RDS security group allows inbound from this EC2 instance"
    echo " - RDS instance is configured for PostgreSQL"
    # We'll continue with deployment even if we can't connect right now
  fi
  
  # Clean up environment variables
  unset PGHOST PGPORT PGUSER PGPASSWORD PGDATABASE
  
  # Skip Nginx and SSL configuration as they're already set up manually
  echo "Skipping Nginx and SSL setup since they are already configured manually"
  
  # Skip PM2 startup configuration as it's already set up
  echo "Using existing PM2 configuration"
  
  # Create deployment flag
  touch "${APP_PATH}/deployed.flag"
fi

# Database Initialization and Migration - Run on every deployment
echo "Initializing database and running migrations..."
cd ${APP_PATH}/backend

# Install production dependencies first
echo "Installing backend dependencies..."
npm install --production

# Check if database initialization script exists
if [ -f "scripts/init-db.js" ]; then
  echo "Found database initialization script. Running database setup..."

  # Set NODE_ENV to production for database initialization
  export NODE_ENV=production

  # Run database initialization (creates database if needed and runs migrations)
  echo "Running database initialization in production mode..."
  if node scripts/init-db.js; then
    echo "✅ Database initialization completed successfully!"

    # Run health check to verify everything is working
    echo "Running post-initialization health check..."
    if npm run health-check; then
      echo "✅ Health check passed - database is ready!"
    else
      echo "⚠️  Health check failed - there may be issues with the database setup"
    fi
  else
    echo "❌ ERROR: Database initialization failed!"
    echo "Check the following:"
    echo " - Database connection parameters in .env"
    echo " - Database server is accessible from EC2"
    echo " - Migration files are valid"
    echo " - Database user has necessary permissions"
    echo " - PostgreSQL server allows database creation"

    echo "Deployment will continue, but database may not be properly initialized."
  fi

  # Clean up environment variable
  unset NODE_ENV
else
  echo "⚠️  Database initialization script not found. Falling back to direct migrations..."

  # Check if migrations directory exists
  if [ -d "migrations" ]; then
    echo "Found migrations directory. Running Knex migrations..."

    # Set NODE_ENV to production for migrations
    export NODE_ENV=production

    # Run migration status check first
    echo "Checking current migration status..."
    if npx knex migrate:status --env production; then
      echo "Migration status check completed."
    else
      echo "Migration status check failed, but continuing with migration..."
    fi

    # Run migrations with production environment
    echo "Running database migrations in production mode..."
    if npx knex migrate:latest --env production; then
      echo "✓ Database migrations completed successfully!"

      # Show final migration status
      echo "Final migration status:"
      npx knex migrate:status --env production || echo "Could not display migration status"

      # Run health check to verify everything is working
      echo "Running post-migration health check..."
      if npm run health-check; then
        echo "✅ Health check passed - database is ready!"
      else
        echo "⚠️  Health check failed - there may be issues with the database setup"
      fi
    else
      echo "❌ ERROR: Database migration failed!"
      echo "Check the following:"
      echo " - Database connection parameters in .env"
      echo " - Database server is accessible from EC2"
      echo " - Migration files are valid"
      echo " - Database user has necessary permissions"

      # Show migration status for debugging
      echo "Current migration status:"
      npx knex migrate:status --env production || echo "Could not display migration status"

      echo "Deployment will continue, but database may not be up to date."
    fi

    # Clean up environment variable
    unset NODE_ENV
  else
    echo "⚠️  No migrations directory found. Skipping database migrations."
    echo "Make sure your migration files are included in the deployment package."
  fi
fi

# Restart services
echo "Restarting services..."

# Restart backend with PM2
echo "Restarting backend application..."
pm2 delete indierepo-api 2>/dev/null || true
pm2 start server.js --name "indierepo-api" --log ${APP_PATH}/logs/backend.log
pm2 save

# Reload Nginx to apply any config changes
echo "Reloading Nginx..."
systemctl reload nginx

# Make sure Nginx is enabled to start on boot
systemctl enable nginx

echo "====================================================="
echo "     Deployment completed successfully!"
echo "     $(date)"
echo "     Visit your configured domain to verify"
echo "====================================================="

# Output version info
if [ -f "${DEPLOY_SRC}/version.txt" ]; then
  echo "Deployment version:"
  cat "${DEPLOY_SRC}/version.txt"
fi
