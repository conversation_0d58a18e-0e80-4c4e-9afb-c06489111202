name: Deploy to AWS EC2

on:
  push:
    branches: [ main ]
  workflow_dispatch:  # Allows manual triggering

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '22'
          cache: 'npm'
          
      # Frontend build
      - name: Install frontend dependencies
        working-directory: ./frontend
        run: npm ci
        
      - name: Build frontend
        working-directory: ./frontend
        run: |
          cp .env.production .env
          npm run build
          
      # Backend prep (no build needed)
      - name: Install backend dependencies
        working-directory: ./backend
        run: npm ci
      
      # Create deployment package
      - name: Create deployment package
        run: |
          mkdir -p deployment
          cp -r backend deployment/
          cp -r frontend/dist deployment/frontend-dist
          cp -r .github/scripts deployment/scripts
          
          # Ensure migrations and database scripts are included
          if [ -d "backend/migrations" ]; then
            echo "✓ Migrations directory found and will be included"
            ls -la backend/migrations/
          else
            echo "⚠️  Warning: No migrations directory found in backend/"
          fi

          if [ -f "backend/scripts/init-db.js" ]; then
            echo "✓ Database initialization script found and will be included"
          else
            echo "⚠️  Warning: Database initialization script not found in backend/scripts/"
          fi
          
          # Create version.txt with build info
          echo "Build: ${{ github.run_number }}" > deployment/version.txt
          echo "Commit: ${{ github.sha }}" >> deployment/version.txt
          echo "Date: $(date)" >> deployment/version.txt
          echo "Environment: production" >> deployment/version.txt
          
          cd deployment
          tar -czf ../indierepo-deploy.tar.gz .
      
      # Set up SSH key for EC2 access (using PEM file)
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.EC2_SSH_KEY }}" > ~/.ssh/ec2.pem
          chmod 600 ~/.ssh/ec2.pem
          echo -e "Host ec2\n\tHostName ${{ secrets.EC2_HOST }}\n\tUser ec2-user\n\tIdentityFile ~/.ssh/ec2.pem\n\tStrictHostKeyChecking no" > ~/.ssh/config
      
      # Copy files to EC2
      - name: Deploy to EC2
        run: |
          scp indierepo-deploy.tar.gz ec2:/tmp/
          ssh ec2 'mkdir -p /tmp/deploy && tar -xzf /tmp/indierepo-deploy.tar.gz -C /tmp/deploy'
          
          # Run deployment script
          ssh ec2 'sudo bash /tmp/deploy/scripts/ec2-deploy.sh'
          
          # Clean up
          ssh ec2 'rm -rf /tmp/indierepo-deploy.tar.gz /tmp/deploy'
          
      # Test deployment
      - name: Verify deployment
        run: |
          echo "Testing API health endpoint..."
          curl -s -o /dev/null -w "%{http_code}" https://${{ secrets.DOMAIN_NAME }}/api/health || true
          echo "Deployment verification complete."
