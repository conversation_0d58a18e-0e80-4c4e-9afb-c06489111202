const fs = require('fs');
const path = require('path');
const zlib = require('zlib');
const logger = require('../config/logger');

/**
 * Get content type based on file extension
 */
function getContentType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  
  switch (ext) {
    case '.js':
      return 'application/javascript';
    case '.wasm':
      return 'application/wasm';
    case '.data':
      return 'application/octet-stream';
    case '.json':
      return 'application/json';
    case '.html':
      return 'text/html';
    case '.css':
      return 'text/css';
    case '.jpg':
    case '.jpeg':
      return 'image/jpeg';
    case '.png':
      return 'image/png';
    case '.gif':
      return 'image/gif';
    case '.svg':
      return 'image/svg+xml';
    default:
      return 'application/octet-stream';
  }
}

/**
 * Decompress and serve gzipped Unity files
 */
function serveGzippedUnityFile(filePath, contentType, res) {
  fs.readFile(filePath, (err, data) => {
    if (err) {
      logger.error(`Error reading Unity file: ${err.message}`);
      return res.status(404).end();
    }
    
    zlib.gunzip(data, (err, decompressedData) => {
      if (err) {
        logger.error(`Error decompressing Unity file: ${err.message}`);
        return res.status(500).end();
      }
      
      res.set('Content-Type', contentType);
      res.send(decompressedData);
    });
  });
}

/**
 * Middleware for Unity WebGL file requests
 */
const unityFileHandler = (req, res, next) => {
  // Only handle Unity WebGL files
  if (!req.path.includes('/files/') || !req.path.includes('web')) {
    return next();
  }

  // Debug logging for Unity files in development
  if (process.env.NODE_ENV === 'development' && 
      process.env.DEBUG_UNITY === 'true') {
    logger.debug(`Unity file requested: ${req.path}`);
  }

  // Handle specific Unity file patterns
  const isUnityBuild = req.path.includes('/Build/');
  const isGzipped = req.path.endsWith('.gz');
  
  if (isUnityBuild && isGzipped) {
    const filePath = path.join(__dirname, '..', '..', req.path);
    const originalPath = req.path.slice(0, -3); // Remove .gz extension
    const contentType = getContentType(originalPath);
    
    logger.debug(`Serving Unity gzipped file: ${path.basename(req.path)}`);
    return serveGzippedUnityFile(filePath, contentType, res);
  }

  next();
};

/**
 * Middleware for general gzipped file handling
 */
const gzipFileHandler = (req, res, next) => {
  if (req.path.endsWith('.gz')) {
    res.set('Content-Encoding', 'gzip');
    const originalPath = req.path.slice(0, -3);
    const contentType = getContentType(originalPath);
    if (contentType) {
      res.set('Content-Type', contentType);
    }
    logger.debug(`Generic gzip handler: ${req.path} as ${contentType || 'auto-detected'}`);
  }
  next();
};

/**
 * Unity game directory redirect handler
 */
const unityGameRedirectHandler = (gameId, gamePath) => {
  return (req, res, next) => {
    const targetPath = `/uploads/games/${gameId}/files/${gamePath}`;
    
    if (req.path === targetPath || req.path === `${targetPath}/`) {
      logger.info(`Redirecting Unity game request to relay.html`);
      return res.redirect(`${targetPath}/relay.html`);
    }
    
    next();
  };
};

module.exports = {
  unityFileHandler,
  gzipFileHandler,
  unityGameRedirectHandler,
  getContentType
}; 