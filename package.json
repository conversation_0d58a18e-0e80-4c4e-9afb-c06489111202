{"name": "indierepo", "version": "1.0.0", "description": "IndieRepo game platform backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"axios": "^1.4.0", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-validator": "^7.0.1", "google-auth-library": "^8.8.0", "jsonwebtoken": "^9.0.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.3.2", "sharp": "^0.32.1", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^2.0.22"}}